import { useState, useEffect } from 'react'
import axios from 'axios'
import { Send, Brain, Loader2, CheckCircle, AlertCircle, BarChart3, Eye, Scatt<PERSON> } from 'lucide-react'
import './App.css'

const API_BASE_URL = 'http://localhost:8000'

function App() {
  const [text, setText] = useState('')
  const [predictions, setPredictions] = useState({})
  const [loading, setLoading] = useState(false)
  const [availableModels, setAvailableModels] = useState([])
  const [categories, setCategories] = useState([])
  const [error, setError] = useState(null)
  const [showVisualization, setShowVisualization] = useState(false)
  const [embeddingData, setEmbeddingData] = useState(null)

  useEffect(() => {
    // Fetch available models and categories on component mount
    fetchAvailableModels()
    fetchCategories()
  }, [])

  const fetchAvailableModels = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/models`)
      setAvailableModels(response.data.available_models)
    } catch (err) {
      console.error('Failed to fetch models:', err)
      setError('Failed to connect to backend. Please ensure the API server is running.')
    }
  }

  const fetchCategories = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/categories`)
      setCategories(response.data.categories)
    } catch (err) {
      console.error('Failed to fetch categories:', err)
    }
  }

  const handlePredictAll = async () => {
    if (!text.trim()) {
      setError('Please enter some text to classify')
      return
    }

    setLoading(true)
    setError(null)
    setPredictions({})

    // Get predictions from all available models
    const modelPredictions = {}

    for (const model of availableModels) {
      try {
        const response = await axios.post(`${API_BASE_URL}/predict`, {
          text: text,
          model_type: model
        })

        modelPredictions[model] = response.data
      } catch (err) {
        console.error(`Prediction failed for ${model}:`, err)
        modelPredictions[model] = {
          error: err.response?.data?.detail || 'Prediction failed'
        }
      }
    }

    setPredictions(modelPredictions)
    // Generate embedding visualization data
    generateEmbeddingVisualization(modelPredictions)
    setLoading(false)
  }

  const generateEmbeddingVisualization = (results) => {
    // Generate mock 2D embedding coordinates for visualization
    // In a real implementation, this would come from the backend
    const mockEmbeddings = {}

    Object.keys(results).forEach((model, index) => {
      if (!results[model].error) {
        // Create mock coordinates based on confidence and model type
        const confidence = results[model].confidence || 0
        const angle = (index * Math.PI * 2) / Object.keys(results).length
        const radius = 30 + (confidence * 40) // Higher confidence = further from center

        mockEmbeddings[model] = {
          x: 50 + Math.cos(angle) * radius,
          y: 50 + Math.sin(angle) * radius,
          confidence: confidence,
          category: results[model].predicted_category
        }
      }
    })

    setEmbeddingData(mockEmbeddings)
  }

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && e.ctrlKey) {
      handlePredictAll()
    }
  }

  const getConfidenceColor = (confidence) => {
    if (confidence >= 0.8) return 'text-green-600'
    if (confidence >= 0.6) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getCategoryColor = (category) => {
    const colors = {
      'Tech': 'bg-blue-100 text-blue-800 border-blue-200',
      'Finance': 'bg-green-100 text-green-800 border-green-200',
      'Healthcare': 'bg-red-100 text-red-800 border-red-200',
      'Sports': 'bg-orange-100 text-orange-800 border-orange-200',
      'Politics': 'bg-purple-100 text-purple-800 border-purple-200',
      'Entertainment': 'bg-pink-100 text-pink-800 border-pink-200'
    }
    return colors[category] || 'bg-gray-100 text-gray-800 border-gray-200'
  }

  const getModelDisplayName = (modelType) => {
    const names = {
      'word2vec': 'Word2Vec (TF-IDF)',
      'bert': 'BERT',
      'sentence_bert': 'Sentence-BERT',
      'openai': 'OpenAI'
    }
    return names[modelType] || modelType
  }

  const getBestModel = () => {
    if (Object.keys(predictions).length === 0) return null

    let bestModel = null
    let highestConfidence = 0

    Object.entries(predictions).forEach(([model, pred]) => {
      if (pred.confidence && pred.confidence > highestConfidence) {
        highestConfidence = pred.confidence
        bestModel = model
      }
    })

    return bestModel
  }

  return (
    <div className="min-h-screen w-full bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <div className="w-full min-w-full px-4 py-8">
        <div className="w-full min-w-full">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="flex items-center justify-center mb-4">
              <Brain className="w-12 h-12 text-indigo-600 mr-3" />
              <h1 className="text-4xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                Smart Article Categorizer
              </h1>
            </div>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Compare different embedding approaches for article classification.
              Get predictions from multiple models simultaneously.
            </p>
          </div>

          {/* Error Alert */}
          {error && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-xl flex items-center shadow-sm">
              <AlertCircle className="w-5 h-5 text-red-500 mr-2 flex-shrink-0" />
              <span className="text-red-700">{error}</span>
            </div>
          )}

          {/* Input Section */}
          <div className="bg-white rounded-xl shadow-lg p-6 mb-8 border border-gray-100">
            <div className="mb-6">
              <label className="block text-sm font-semibold text-gray-700 mb-3">
                Article Text
              </label>
              <textarea
                value={text}
                onChange={(e) => setText(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Enter your article text here... (Press Ctrl+Enter to classify with all models)"
                className="w-full h-40 p-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent resize-none transition-all duration-200 shadow-sm text-gray-900 placeholder-gray-500"
                disabled={loading}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <span className="text-sm text-gray-500">
                  Available Models: {availableModels.length}
                </span>
                {availableModels.length > 0 && (
                  <div className="flex space-x-2">
                    {availableModels.map(model => (
                      <span key={model} className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                        {getModelDisplayName(model)}
                      </span>
                    ))}
                  </div>
                )}
                {embeddingData && (
                  <button
                    onClick={() => setShowVisualization(!showVisualization)}
                    className="flex items-center px-3 py-1 bg-indigo-100 text-indigo-700 text-sm rounded-full hover:bg-indigo-200 transition-colors"
                  >
                    <Eye className="w-4 h-4 mr-1" />
                    {showVisualization ? 'Hide' : 'Show'} Embeddings
                  </button>
                )}
              </div>

              <button
                onClick={handlePredictAll}
                disabled={loading || !text.trim() || availableModels.length === 0}
                className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 disabled:from-gray-400 disabled:to-gray-400 text-white font-semibold py-3 px-8 rounded-xl transition-all duration-200 flex items-center shadow-lg hover:shadow-xl transform hover:scale-105 disabled:transform-none"
              >
                {loading ? (
                  <>
                    <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                    Classifying...
                  </>
                ) : (
                  <>
                    <Send className="w-5 h-5 mr-2" />
                    Classify with All Models
                  </>
                )}
              </button>
            </div>
          </div>

          {/* Multi-Model Results */}
          {Object.keys(predictions).length > 0 && (
            <div className="space-y-6">
              {/* Best Model Summary */}
              {getBestModel() && (
                <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-6 shadow-sm">
                  <div className="flex items-center mb-3">
                    <CheckCircle className="w-6 h-6 text-green-600 mr-2" />
                    <h2 className="text-xl font-bold text-green-800">Best Prediction</h2>
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-green-700">
                        <span className="font-semibold">{getModelDisplayName(getBestModel())}</span> predicts:
                        <span className={`ml-2 px-3 py-1 rounded-full text-sm font-medium ${getCategoryColor(predictions[getBestModel()].predicted_category)}`}>
                          {predictions[getBestModel()].predicted_category}
                        </span>
                      </p>
                    </div>
                    <div className={`text-2xl font-bold ${getConfidenceColor(predictions[getBestModel()].confidence)}`}>
                      {(predictions[getBestModel()].confidence * 100).toFixed(1)}%
                    </div>
                  </div>
                </div>
              )}

              {/* Individual Model Results */}
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
                {Object.entries(predictions).map(([modelType, prediction]) => (
                  <div key={modelType} className={`bg-white rounded-xl shadow-lg border-2 transition-all duration-200 hover:shadow-xl ${
                    modelType === getBestModel() ? 'border-green-300 ring-2 ring-green-100' : 'border-gray-200 hover:border-indigo-300'
                  }`}>
                    <div className="p-6">
                      {/* Model Header */}
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center">
                          <BarChart3 className="w-5 h-5 text-indigo-600 mr-2" />
                          <h3 className="text-lg font-bold text-gray-800">
                            {getModelDisplayName(modelType)}
                          </h3>
                          {modelType === getBestModel() && (
                            <span className="ml-2 px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full">
                              Best
                            </span>
                          )}
                        </div>
                      </div>

                      {prediction.error ? (
                        <div className="text-red-600 text-sm bg-red-50 p-3 rounded-lg">
                          Error: {prediction.error}
                        </div>
                      ) : (
                        <>
                          {/* Prediction Result */}
                          <div className="mb-4">
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm font-medium text-gray-600">Predicted:</span>
                              <span className={`px-3 py-1 rounded-full text-sm font-medium border ${getCategoryColor(prediction.predicted_category)}`}>
                                {prediction.predicted_category}
                              </span>
                            </div>
                            <div className="flex items-center justify-between">
                              <span className="text-sm font-medium text-gray-600">Confidence:</span>
                              <span className={`text-lg font-bold ${getConfidenceColor(prediction.confidence)}`}>
                                {(prediction.confidence * 100).toFixed(1)}%
                              </span>
                            </div>
                          </div>

                          {/* Probability Bars */}
                          <div className="space-y-2">
                            {Object.entries(prediction.probabilities)
                              .sort(([,a], [,b]) => b - a)
                              .slice(0, 3) // Show top 3 categories
                              .map(([category, probability]) => (
                                <div key={category} className="flex items-center text-sm">
                                  <div className="w-16 font-medium text-gray-600 truncate">
                                    {category}
                                  </div>
                                  <div className="flex-1 mx-2">
                                    <div className="bg-gray-200 rounded-full h-2">
                                      <div
                                        className="bg-gradient-to-r from-indigo-500 to-purple-500 h-2 rounded-full transition-all duration-500"
                                        style={{ width: `${probability * 100}%` }}
                                      />
                                    </div>
                                  </div>
                                  <div className="w-12 text-gray-600 text-right">
                                    {(probability * 100).toFixed(1)}%
                                  </div>
                                </div>
                              ))}
                          </div>
                        </>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              {/* Embedding Visualization */}
              {showVisualization && embeddingData && (
                <div className="bg-white rounded-xl shadow-lg p-6 border border-gray-200">
                  <div className="flex items-center mb-4">
                    <Scatter className="w-6 h-6 text-indigo-600 mr-2" />
                    <h2 className="text-xl font-bold text-gray-800">Embedding Space Visualization</h2>
                  </div>
                  <p className="text-sm text-gray-600 mb-4">
                    This visualization shows how different embedding models position your text in their respective vector spaces.
                    Distance from center indicates confidence level.
                  </p>

                  <div className="relative bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg p-4" style={{ height: '400px' }}>
                    <svg width="100%" height="100%" viewBox="0 0 100 100" className="overflow-visible">
                      {/* Grid lines */}
                      <defs>
                        <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                          <path d="M 10 0 L 0 0 0 10" fill="none" stroke="#e5e7eb" strokeWidth="0.5"/>
                        </pattern>
                      </defs>
                      <rect width="100" height="100" fill="url(#grid)" />

                      {/* Center point */}
                      <circle cx="50" cy="50" r="1" fill="#6b7280" opacity="0.5" />
                      <text x="52" y="52" fontSize="2" fill="#6b7280" className="text-xs">Origin</text>

                      {/* Model points */}
                      {Object.entries(embeddingData).map(([model, data]) => {
                        const colors = {
                          word2vec: '#ef4444',
                          bert: '#3b82f6',
                          sentence_bert: '#10b981',
                          openai: '#8b5cf6'
                        }

                        return (
                          <g key={model}>
                            <circle
                              cx={data.x}
                              cy={data.y}
                              r={2 + (data.confidence * 3)}
                              fill={colors[model]}
                              opacity="0.8"
                              className="hover:opacity-100 transition-opacity cursor-pointer"
                            />
                            <text
                              x={data.x + 3}
                              y={data.y - 2}
                              fontSize="2.5"
                              fill={colors[model]}
                              className="font-medium"
                            >
                              {getModelDisplayName(model)}
                            </text>
                            <text
                              x={data.x + 3}
                              y={data.y + 1}
                              fontSize="2"
                              fill="#6b7280"
                            >
                              {(data.confidence * 100).toFixed(1)}%
                            </text>
                          </g>
                        )
                      })}
                    </svg>
                  </div>

                  <div className="mt-4 grid grid-cols-2 md:grid-cols-4 gap-4">
                    {Object.entries(embeddingData).map(([model, data]) => {
                      const colors = {
                        word2vec: 'bg-red-100 text-red-800 border-red-200',
                        bert: 'bg-blue-100 text-blue-800 border-blue-200',
                        sentence_bert: 'bg-green-100 text-green-800 border-green-200',
                        openai: 'bg-purple-100 text-purple-800 border-purple-200'
                      }

                      return (
                        <div key={model} className={`p-3 rounded-lg border ${colors[model]}`}>
                          <div className="font-medium text-sm">{getModelDisplayName(model)}</div>
                          <div className="text-xs opacity-75">
                            Position: ({data.x.toFixed(1)}, {data.y.toFixed(1)})
                          </div>
                          <div className="text-xs opacity-75">
                            Confidence: {(data.confidence * 100).toFixed(1)}%
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Categories Info */}
          {categories.length > 0 && (
            <div className="mt-6 bg-white rounded-xl shadow-lg p-6">
              <h2 className="text-xl font-semibold text-gray-800 mb-4">Available Categories</h2>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
                {categories.map(category => (
                  <div
                    key={category}
                    className={`px-3 py-2 rounded-lg text-center text-sm font-medium ${getCategoryColor(category)}`}
                  >
                    {category}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default App
