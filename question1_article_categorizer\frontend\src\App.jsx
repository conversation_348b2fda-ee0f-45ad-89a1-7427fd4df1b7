import { useState, useEffect } from 'react'
import axios from 'axios'
import { Send, Brain, Loader2, CheckCircle, AlertCircle } from 'lucide-react'
import './App.css'

const API_BASE_URL = 'http://localhost:8000'

function App() {
  const [text, setText] = useState('')
  const [selectedModel, setSelectedModel] = useState('bert')
  const [prediction, setPrediction] = useState(null)
  const [loading, setLoading] = useState(false)
  const [availableModels, setAvailableModels] = useState([])
  const [categories, setCategories] = useState([])
  const [error, setError] = useState(null)

  useEffect(() => {
    // Fetch available models and categories on component mount
    fetchAvailableModels()
    fetchCategories()
  }, [])

  const fetchAvailableModels = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/models`)
      setAvailableModels(response.data.available_models)
      if (response.data.available_models.length > 0) {
        setSelectedModel(response.data.available_models[0])
      }
    } catch (err) {
      console.error('Failed to fetch models:', err)
      setError('Failed to connect to backend. Please ensure the API server is running.')
    }
  }

  const fetchCategories = async () => {
    try {
      const response = await axios.get(`${API_BASE_URL}/categories`)
      setCategories(response.data.categories)
    } catch (err) {
      console.error('Failed to fetch categories:', err)
    }
  }

  const handlePredict = async () => {
    if (!text.trim()) {
      setError('Please enter some text to classify')
      return
    }

    setLoading(true)
    setError(null)

    try {
      const response = await axios.post(`${API_BASE_URL}/predict`, {
        text: text,
        model_type: selectedModel
      })

      setPrediction(response.data)
    } catch (err) {
      console.error('Prediction failed:', err)
      setError(err.response?.data?.detail || 'Prediction failed. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && e.ctrlKey) {
      handlePredict()
    }
  }

  const getConfidenceColor = (confidence) => {
    if (confidence >= 0.8) return 'text-green-600'
    if (confidence >= 0.6) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getCategoryColor = (category) => {
    const colors = {
      'Tech': 'bg-blue-100 text-blue-800',
      'Finance': 'bg-green-100 text-green-800',
      'Healthcare': 'bg-red-100 text-red-800',
      'Sports': 'bg-orange-100 text-orange-800',
      'Politics': 'bg-purple-100 text-purple-800',
      'Entertainment': 'bg-pink-100 text-pink-800'
    }
    return colors[category] || 'bg-gray-100 text-gray-800'
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="flex items-center justify-center mb-4">
              <Brain className="w-12 h-12 text-indigo-600 mr-3" />
              <h1 className="text-4xl font-bold text-gray-800">Smart Article Categorizer</h1>
            </div>
            <p className="text-lg text-gray-600">
              Classify articles using advanced embedding techniques
            </p>
          </div>

          {/* Error Alert */}
          {error && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg flex items-center">
              <AlertCircle className="w-5 h-5 text-red-500 mr-2" />
              <span className="text-red-700">{error}</span>
            </div>
          )}

          {/* Main Card */}
          <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
            {/* Model Selection */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select Embedding Model
              </label>
              <select
                value={selectedModel}
                onChange={(e) => setSelectedModel(e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                disabled={loading}
              >
                {availableModels.map(model => (
                  <option key={model} value={model}>
                    {model.charAt(0).toUpperCase() + model.slice(1).replace('_', '-')}
                  </option>
                ))}
              </select>
            </div>

            {/* Text Input */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Article Text
              </label>
              <textarea
                value={text}
                onChange={(e) => setText(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Enter your article text here... (Press Ctrl+Enter to classify)"
                className="w-full h-40 p-4 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent resize-none"
                disabled={loading}
              />
            </div>

            {/* Predict Button */}
            <button
              onClick={handlePredict}
              disabled={loading || !text.trim()}
              className="w-full bg-indigo-600 hover:bg-indigo-700 disabled:bg-gray-400 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center"
            >
              {loading ? (
                <>
                  <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                  Classifying...
                </>
              ) : (
                <>
                  <Send className="w-5 h-5 mr-2" />
                  Classify Article
                </>
              )}
            </button>
          </div>

          {/* Results */}
          {prediction && (
            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="flex items-center mb-4">
                <CheckCircle className="w-6 h-6 text-green-500 mr-2" />
                <h2 className="text-xl font-semibold text-gray-800">Classification Results</h2>
              </div>

              {/* Predicted Category */}
              <div className="mb-6">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-gray-600">Predicted Category:</span>
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getCategoryColor(prediction.predicted_category)}`}>
                    {prediction.predicted_category}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-600">Confidence:</span>
                  <span className={`text-lg font-bold ${getConfidenceColor(prediction.confidence)}`}>
                    {(prediction.confidence * 100).toFixed(1)}%
                  </span>
                </div>
              </div>

              {/* Probability Distribution */}
              <div>
                <h3 className="text-lg font-medium text-gray-800 mb-3">Probability Distribution</h3>
                <div className="space-y-2">
                  {Object.entries(prediction.probabilities)
                    .sort(([,a], [,b]) => b - a)
                    .map(([category, probability]) => (
                      <div key={category} className="flex items-center">
                        <div className="w-20 text-sm font-medium text-gray-600">
                          {category}
                        </div>
                        <div className="flex-1 mx-3">
                          <div className="bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-indigo-600 h-2 rounded-full transition-all duration-500"
                              style={{ width: `${probability * 100}%` }}
                            />
                          </div>
                        </div>
                        <div className="w-12 text-sm text-gray-600 text-right">
                          {(probability * 100).toFixed(1)}%
                        </div>
                      </div>
                    ))}
                </div>
              </div>

              {/* Model Info */}
              <div className="mt-6 pt-4 border-t border-gray-200">
                <p className="text-sm text-gray-500">
                  Classified using: <span className="font-medium">{prediction.model_type}</span>
                </p>
              </div>
            </div>
          )}

          {/* Categories Info */}
          {categories.length > 0 && (
            <div className="mt-6 bg-white rounded-xl shadow-lg p-6">
              <h2 className="text-xl font-semibold text-gray-800 mb-4">Available Categories</h2>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {categories.map(category => (
                  <div
                    key={category}
                    className={`px-3 py-2 rounded-lg text-center text-sm font-medium ${getCategoryColor(category)}`}
                  >
                    {category}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default App
