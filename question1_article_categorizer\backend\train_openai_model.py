"""
Train OpenAI embedding model for article classification.
"""

import pandas as pd
import numpy as np
from pathlib import Path
import os
import sys

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from embeddings import get_embedding_model
from classifier import ArticleClassifier

# OpenAI API Key - should be set as environment variable
OPENAI_API_KEY = os.environ.get('OPENAI_API_KEY')
if not OPENAI_API_KEY:
    raise ValueError("Please set OPENAI_API_KEY environment variable")

def main():
    """Train OpenAI embedding model."""
    
    # Set up paths
    project_dir = Path(__file__).parent.parent
    data_dir = project_dir / "data"
    models_dir = project_dir / "models"
    
    # Load data
    print("Loading training data...")
    train_df = pd.read_csv(data_dir / "train_dataset.csv")
    test_df = pd.read_csv(data_dir / "test_dataset.csv")
    
    print(f"Training samples: {len(train_df)}")
    print(f"Test samples: {len(test_df)}")
    print(f"Categories: {train_df['category'].unique()}")
    
    # Initialize OpenAI embedding model
    print("\nInitializing OpenAI embedding model...")
    try:
        # Set environment variable for API key
        os.environ['OPENAI_API_KEY'] = OPENAI_API_KEY

        # Create classifier
        classifier = ArticleClassifier(embedding_type="openai")
        
        # Train the classifier
        print("Training classifier with OpenAI embeddings...")
        print("Note: This will make API calls to OpenAI and may take some time...")

        results = classifier.train(train_df)

        print(f"\nTraining Results: {results}")

        # Additional test on test set
        print("\nTesting on separate test set...")
        test_texts = test_df['text'].tolist()
        test_labels = test_df['category'].tolist()

        predictions, confidence_scores = classifier.predict(test_texts)

        # Calculate metrics
        from sklearn.metrics import accuracy_score, classification_report, confusion_matrix

        accuracy = accuracy_score(test_labels, predictions)
        print(f"\nSeparate Test Set Accuracy: {accuracy:.4f}")

        print("\nClassification Report:")
        print(classification_report(test_labels, predictions))

        print("\nConfusion Matrix:")
        print(confusion_matrix(test_labels, predictions))
        
        # Save the model
        print(f"\nSaving model...")
        classifier.save_model()
        
        print("OpenAI model training completed successfully!")
        
        # Test a sample prediction
        print("\n" + "="*50)
        print("Testing sample prediction:")
        sample_text = "Apple announced new iPhone features with advanced AI capabilities and improved camera technology."
        predictions, confidence = classifier.predict([sample_text])
        prediction = predictions[0]

        print(f"Sample text: {sample_text}")
        print(f"Predicted category: {prediction}")
        print(f"Confidence: {confidence[0]:.4f}")

        # Get detailed probabilities
        detailed_results = classifier.predict_with_probabilities([sample_text])
        print("Detailed probabilities:")
        for category, prob in detailed_results[0]['probabilities'].items():
            print(f"  {category}: {prob:.4f}")
            
    except Exception as e:
        print(f"Error training OpenAI model: {e}")
        print("Please check your API key and internet connection.")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ OpenAI model training completed successfully!")
    else:
        print("\n❌ OpenAI model training failed!")
