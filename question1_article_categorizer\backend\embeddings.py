"""
Embedding implementations for article categorization.
Supports Word2Vec, BERT, Sentence-BERT, and OpenAI embeddings.
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Optional
import pickle
import os
from abc import ABC, abstractmethod

# Word2Vec/GloVe imports (simplified version using TF-IDF)
from sklearn.feature_extraction.text import TfidfVectorizer
import nltk
from nltk.corpus import stopwords
from nltk.tokenize import word_tokenize

# BERT imports
import torch
from transformers import AutoTokenizer, AutoModel

# Sentence-BERT imports
from sentence_transformers import SentenceTransformer

# OpenAI imports (commented out for now)
# import openai
# from openai import OpenAI
# import time

# Configuration
from config import MODEL_CONFIGS, MODELS_DIR

class BaseEmbedding(ABC):
    """Base class for all embedding implementations."""
    
    def __init__(self, config: Dict):
        self.config = config
        self.model = None
        self.is_trained = False
    
    @abstractmethod
    def fit(self, texts: List[str]) -> None:
        """Train/fit the embedding model on texts."""
        pass
    
    @abstractmethod
    def transform(self, texts: List[str]) -> np.ndarray:
        """Transform texts to embeddings."""
        pass
    
    def fit_transform(self, texts: List[str]) -> np.ndarray:
        """Fit and transform texts."""
        self.fit(texts)
        return self.transform(texts)
    
    def save_model(self, path: str) -> None:
        """Save the trained model."""
        os.makedirs(os.path.dirname(path), exist_ok=True)
        with open(path, 'wb') as f:
            pickle.dump(self.model, f)
    
    def load_model(self, path: str) -> None:
        """Load a trained model."""
        if os.path.exists(path):
            with open(path, 'rb') as f:
                self.model = pickle.load(f)
            self.is_trained = True
            return True
        return False

class Word2VecEmbedding(BaseEmbedding):
    """Simplified Word2Vec embedding using TF-IDF (as substitute for actual Word2Vec)."""

    def __init__(self, config: Dict):
        super().__init__(config)
        # Download required NLTK data
        try:
            nltk.data.find('tokenizers/punkt')
        except LookupError:
            nltk.download('punkt')

        try:
            nltk.data.find('corpora/stopwords')
        except LookupError:
            nltk.download('stopwords')

        self.stop_words = set(stopwords.words('english'))

        # Use TF-IDF as a substitute for Word2Vec
        self.model = TfidfVectorizer(
            max_features=self.config['vector_size'],
            stop_words='english',
            ngram_range=(1, 2),
            lowercase=True
        )

    def preprocess_text(self, text: str) -> str:
        """Preprocess text for TF-IDF."""
        # Basic preprocessing
        text = text.lower()
        # Remove extra whitespace
        text = ' '.join(text.split())
        return text

    def fit(self, texts: List[str]) -> None:
        """Train TF-IDF model on texts."""
        print("Training TF-IDF model (Word2Vec substitute)...")

        # Preprocess all texts
        processed_texts = [self.preprocess_text(text) for text in texts]

        # Fit TF-IDF model
        self.model.fit(processed_texts)

        self.is_trained = True
        print(f"TF-IDF model trained with vocabulary size: {len(self.model.vocabulary_)}")

    def transform(self, texts: List[str]) -> np.ndarray:
        """Transform texts to TF-IDF embeddings."""
        if not self.is_trained:
            raise ValueError("Model must be trained before transformation")

        # Preprocess texts
        processed_texts = [self.preprocess_text(text) for text in texts]

        # Transform to TF-IDF vectors
        embeddings = self.model.transform(processed_texts).toarray()

        return embeddings

class BERTEmbedding(BaseEmbedding):
    """BERT embedding using [CLS] token."""

    def __init__(self, config: Dict):
        super().__init__(config)
        self.tokenizer = None
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"Using device: {self.device}")

    def fit(self, texts: List[str]) -> None:
        """Load pre-trained BERT model."""
        print("Loading BERT model...")

        self.tokenizer = AutoTokenizer.from_pretrained(self.config['model_name'])
        self.model = AutoModel.from_pretrained(self.config['model_name'])
        self.model.to(self.device)
        self.model.eval()

        self.is_trained = True
        print("BERT model loaded successfully")

    def transform(self, texts: List[str]) -> np.ndarray:
        """Transform texts to BERT embeddings using [CLS] token."""
        if not self.is_trained:
            raise ValueError("Model must be loaded before transformation")

        embeddings = []

        # Process in batches
        batch_size = self.config['batch_size']

        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i + batch_size]

            # Tokenize batch
            encoded = self.tokenizer(
                batch_texts,
                padding=True,
                truncation=True,
                max_length=self.config['max_length'],
                return_tensors='pt'
            )

            # Move to device
            encoded = {key: val.to(self.device) for key, val in encoded.items()}

            # Get embeddings
            with torch.no_grad():
                outputs = self.model(**encoded)
                # Use [CLS] token embedding (first token)
                cls_embeddings = outputs.last_hidden_state[:, 0, :].cpu().numpy()
                embeddings.extend(cls_embeddings)

        return np.array(embeddings)

class SentenceBERTEmbedding(BaseEmbedding):
    """Sentence-BERT embedding for direct sentence embeddings."""

    def __init__(self, config: Dict):
        super().__init__(config)

    def fit(self, texts: List[str]) -> None:
        """Load pre-trained Sentence-BERT model."""
        print("Loading Sentence-BERT model...")

        self.model = SentenceTransformer(self.config['model_name'])
        self.is_trained = True

        print("Sentence-BERT model loaded successfully")

    def transform(self, texts: List[str]) -> np.ndarray:
        """Transform texts to Sentence-BERT embeddings."""
        if not self.is_trained:
            raise ValueError("Model must be loaded before transformation")

        # Get embeddings
        embeddings = self.model.encode(
            texts,
            batch_size=self.config['batch_size'],
            show_progress_bar=True
        )

        return embeddings

# Commented out for now - requires OpenAI API
# class OpenAIEmbedding(BaseEmbedding):
#     """OpenAI embedding using text-embedding-ada-002."""
#
#     def __init__(self, config: Dict):
#         super().__init__(config)
#         self.client = None
#
#         # Get API key from environment
#         api_key = os.getenv(config['api_key_env'])
#         if not api_key:
#             raise ValueError(f"Please set {config['api_key_env']} environment variable")
#
#         self.client = OpenAI(api_key=api_key)
#
#     def fit(self, texts: List[str]) -> None:
#         """OpenAI embeddings don't require training."""
#         self.is_trained = True
#         print("OpenAI embedding client initialized")
#
#     def transform(self, texts: List[str]) -> np.ndarray:
#         """Transform texts to OpenAI embeddings."""
#         if not self.is_trained:
#             raise ValueError("Client must be initialized before transformation")
#
#         embeddings = []
#
#         for text in texts:
#             try:
#                 # Get embedding from OpenAI API
#                 response = self.client.embeddings.create(
#                     model=self.config['model_name'],
#                     input=text
#                 )
#
#                 embedding = response.data[0].embedding
#                 embeddings.append(embedding)
#
#                 # Rate limiting
#                 time.sleep(0.1)
#
#             except Exception as e:
#                 print(f"Error getting embedding for text: {e}")
#                 # Use zero vector as fallback
#                 embeddings.append(np.zeros(1536))  # ada-002 dimension
#
#         return np.array(embeddings)

def get_embedding_model(model_type: str) -> BaseEmbedding:
    """Factory function to get embedding model by type."""
    config = MODEL_CONFIGS[model_type]

    if model_type == "word2vec":
        return Word2VecEmbedding(config)
    elif model_type == "bert":
        return BERTEmbedding(config)
    elif model_type == "sentence_bert":
        return SentenceBERTEmbedding(config)
    # elif model_type == "openai":
    #     return OpenAIEmbedding(config)
    else:
        raise ValueError(f"Model type '{model_type}' not available. Supported types: 'word2vec', 'bert', 'sentence_bert'")

if __name__ == "__main__":
    # Test embedding implementations
    sample_texts = [
        "This is a technology article about artificial intelligence.",
        "The stock market showed strong performance today.",
        "New medical research reveals breakthrough in cancer treatment."
    ]
    
    # Test Word2Vec
    print("Testing Word2Vec embedding...")
    w2v = get_embedding_model("word2vec")
    w2v.fit(sample_texts)
    embeddings = w2v.transform(sample_texts)
    print(f"Word2Vec embeddings shape: {embeddings.shape}")
