{"version": 3, "file": "options.js", "sourceRoot": "", "sources": ["../../src/options.ts"], "names": [], "mappings": ";AAAA,0EAA0E;;;AAQ1E,MAAM,MAAM,GAAG,IAAI,GAAG,CACpB;IACE,CAAC,GAAG,EAAE,KAAK,CAAC;IACZ,CAAC,GAAG,EAAE,MAAM,CAAC;IACb,CAAC,GAAG,EAAE,MAAM,CAAC;IACb,CAAC,GAAG,EAAE,eAAe,CAAC;IACtB,CAAC,GAAG,EAAE,QAAQ,CAAC;IACf,CAAC,kBAAkB,EAAE,OAAO,CAAC;IAC7B,CAAC,iBAAiB,EAAE,OAAO,CAAC;IAC5B,CAAC,YAAY,EAAE,OAAO,CAAC;IACvB,CAAC,WAAW,EAAE,OAAO,CAAC;IACtB,CAAC,kBAAkB,EAAE,OAAO,CAAC;IAC7B,CAAC,gBAAgB,EAAE,OAAO,CAAC;IAC3B,CAAC,GAAG,EAAE,MAAM,CAAC;IACb,CAAC,eAAe,EAAE,MAAM,CAAC;IACzB,CAAC,cAAc,EAAE,MAAM,CAAC;IACxB,CAAC,GAAG,EAAE,SAAS,CAAC;IAChB,CAAC,UAAU,EAAE,SAAS,CAAC;IACvB,CAAC,GAAG,EAAE,eAAe,CAAC;IACtB,CAAC,GAAG,EAAE,QAAQ,CAAC;IACf,CAAC,GAAG,EAAE,QAAQ,CAAC;IACf,CAAC,SAAS,EAAE,aAAa,CAAC;CAC3B,CACF,CAAA;AAonBM,MAAM,UAAU,GAAG,CACxB,CAAI,EACyB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;AAFzC,QAAA,UAAU,cAE+B;AAC/C,MAAM,WAAW,GAAG,CACzB,CAAI,EAC0B,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;AAFzC,QAAA,WAAW,eAE8B;AAC/C,MAAM,YAAY,GAAG,CAC1B,CAAI,EAC2B,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAA;AAF1C,QAAA,YAAY,gBAE8B;AAChD,MAAM,aAAa,GAAG,CAC3B,CAAI,EAC4B,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAA;AAF1C,QAAA,aAAa,iBAE6B;AAChD,MAAM,MAAM,GAAG,CACpB,CAAI,EACqB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;AAFzB,QAAA,MAAM,UAEmB;AAC/B,MAAM,OAAO,GAAG,CACrB,CAAI,EACsB,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;AAFzB,QAAA,OAAO,WAEkB;AAC/B,MAAM,MAAM,GAAG,CACpB,CAAI,EACqB,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;AAFzB,QAAA,MAAM,UAEmB;AAC/B,MAAM,QAAQ,GAAG,CACtB,CAAI,EACuB,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;AAF1B,QAAA,QAAQ,YAEkB;AAEvC,MAAM,UAAU,GAAG,CACjB,CAA8B,EACZ,EAAE;IACpB,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;IACvB,IAAI,CAAC;QAAE,OAAO,CAAC,CAAA;IACf,OAAO,CAAqB,CAAA;AAC9B,CAAC,CAAA;AAEM,MAAM,OAAO,GAAG,CACrB,MAA6B,EAAE,EACnB,EAAE;IACd,IAAI,CAAC,GAAG;QAAE,OAAO,EAAE,CAAA;IACnB,MAAM,MAAM,GAAwB,EAAE,CAAA;IACtC,KAAK,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,GAAG,CAGtC,EAAE,CAAC;QACJ,oEAAoE;QACpE,MAAM,CAAC,GAAG,UAAU,CAAC,GAAG,CAAC,CAAA;QACzB,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAA;IACf,CAAC;IACD,6CAA6C;IAC7C,IAAI,MAAM,CAAC,KAAK,KAAK,SAAS,IAAI,MAAM,CAAC,OAAO,KAAK,KAAK,EAAE,CAAC;QAC3D,MAAM,CAAC,KAAK,GAAG,IAAI,CAAA;IACrB,CAAC;IACD,OAAO,MAAM,CAAC,OAAO,CAAA;IACrB,OAAO,MAAoB,CAAA;AAC7B,CAAC,CAAA;AAnBY,QAAA,OAAO,WAmBnB", "sourcesContent": ["// turn tar(1) style args like `C` into the more verbose things like `cwd`\n\nimport { type GzipOptions, type ZlibOptions } from 'minizlib'\nimport { type Stats } from 'node:fs'\nimport { type ReadEntry } from './read-entry.js'\nimport { type WarnData } from './warn-method.js'\nimport { WriteEntry } from './write-entry.js'\n\nconst argmap = new Map<keyof TarOptionsWithAliases, keyof TarOptions>(\n  [\n    ['C', 'cwd'],\n    ['f', 'file'],\n    ['z', 'gzip'],\n    ['P', 'preservePaths'],\n    ['U', 'unlink'],\n    ['strip-components', 'strip'],\n    ['stripComponents', 'strip'],\n    ['keep-newer', 'newer'],\n    ['keepNewer', 'newer'],\n    ['keep-newer-files', 'newer'],\n    ['keepNewerFiles', 'newer'],\n    ['k', 'keep'],\n    ['keep-existing', 'keep'],\n    ['keepExisting', 'keep'],\n    ['m', 'noMtime'],\n    ['no-mtime', 'noMtime'],\n    ['p', 'preserveOwner'],\n    ['L', 'follow'],\n    ['h', 'follow'],\n    ['onentry', 'onReadEntry'],\n  ],\n)\n\n/**\n * The options that can be provided to tar commands.\n *\n * Note that some of these are only relevant for certain commands, since\n * they are specific to reading or writing.\n *\n * Aliases are provided in the {@link TarOptionsWithAliases} type.\n */\nexport interface TarOptions {\n  //////////////////////////\n  // shared options\n\n  /**\n   * Perform all I/O operations synchronously. If the stream is ended\n   * immediately, then it will be processed entirely synchronously.\n   */\n  sync?: boolean\n\n  /**\n   * The tar file to be read and/or written. When this is set, a stream\n   * is not returned. Asynchronous commands will return a promise indicating\n   * when the operation is completed, and synchronous commands will return\n   * immediately.\n   */\n  file?: string\n\n  /**\n   * Treat warnings as crash-worthy errors. Defaults false.\n   */\n  strict?: boolean\n\n  /**\n   * The effective current working directory for this tar command\n   */\n  cwd?: string\n\n  /**\n   * When creating a tar archive, this can be used to compress it as well.\n   * Set to `true` to use the default gzip options, or customize them as\n   * needed.\n   *\n   * When reading, if this is unset, then the compression status will be\n   * inferred from the archive data. This is generally best, unless you are\n   * sure of the compression settings in use to create the archive, and want to\n   * fail if the archive doesn't match expectations.\n   */\n  gzip?: boolean | GzipOptions\n\n  /**\n   * When creating archives, preserve absolute and `..` paths in the archive,\n   * rather than sanitizing them under the cwd.\n   *\n   * When extracting, allow absolute paths, paths containing `..`, and\n   * extracting through symbolic links. By default, the root `/` is stripped\n   * from absolute paths (eg, turning `/x/y/z` into `x/y/z`), paths containing\n   * `..` are not extracted, and any file whose location would be modified by a\n   * symbolic link is not extracted.\n   *\n   * **WARNING** This is almost always unsafe, and must NEVER be used on\n   * archives from untrusted sources, such as user input, and every entry must\n   * be validated to ensure it is safe to write. Even if the input is not\n   * malicious, mistakes can cause a lot of damage!\n   */\n  preservePaths?: boolean\n\n  /**\n   * When extracting, do not set the `mtime` value for extracted entries to\n   * match the `mtime` in the archive.\n   *\n   * When creating archives, do not store the `mtime` value in the entry. Note\n   * that this prevents properly using other mtime-based features (such as\n   * `tar.update` or the `newer` option) with the resulting archive.\n   */\n  noMtime?: boolean\n\n  /**\n   * Set to `true` or an object with settings for `zlib.BrotliCompress()` to\n   * create a brotli-compressed archive\n   *\n   * When extracting, this will cause the archive to be treated as a\n   * brotli-compressed file if set to `true` or a ZlibOptions object.\n   *\n   * If set `false`, then brotli options will not be used.\n   *\n   * If both this and the `gzip` option are left `undefined`, then tar will\n   * attempt to infer the brotli compression status, but can only do so based\n   * on the filename. If the filename ends in `.tbr` or `.tar.br`, and the\n   * first 512 bytes are not a valid tar header, then brotli decompression\n   * will be attempted.\n   */\n  brotli?: boolean | ZlibOptions\n\n  /**\n   * A function that is called with `(path, stat)` when creating an archive, or\n   * `(path, entry)` when extracting. Return true to process the file/entry, or\n   * false to exclude it.\n   */\n  filter?: (path: string, entry: Stats | ReadEntry) => boolean\n\n  /**\n   * A function that gets called for any warning encountered.\n   *\n   * Note: if `strict` is set, then the warning will throw, and this method\n   * will not be called.\n   */\n  onwarn?: (code: string, message: string, data: WarnData) => any\n\n  //////////////////////////\n  // extraction options\n\n  /**\n   * When extracting, unlink files before creating them. Without this option,\n   * tar overwrites existing files, which preserves existing hardlinks. With\n   * this option, existing hardlinks will be broken, as will any symlink that\n   * would affect the location of an extracted file.\n   */\n  unlink?: boolean\n\n  /**\n   * When extracting, strip the specified number of path portions from the\n   * entry path. For example, with `{strip: 2}`, the entry `a/b/c/d` would be\n   * extracted to `{cwd}/c/d`.\n   *\n   * Any entry whose entire path is stripped will be excluded.\n   */\n  strip?: number\n\n  /**\n   * When extracting, keep the existing file on disk if it's newer than the\n   * file in the archive.\n   */\n  newer?: boolean\n\n  /**\n   * When extracting, do not overwrite existing files at all.\n   */\n  keep?: boolean\n\n  /**\n   * When extracting, set the `uid` and `gid` of extracted entries to the `uid`\n   * and `gid` fields in the archive. Defaults to true when run as root, and\n   * false otherwise.\n   *\n   * If false, then files and directories will be set with the owner and group\n   * of the user running the process. This is similar to `-p` in `tar(1)`, but\n   * ACLs and other system-specific data is never unpacked in this\n   * implementation, and modes are set by default already.\n   */\n  preserveOwner?: boolean\n\n  /**\n   * The maximum depth of subfolders to extract into. This defaults to 1024.\n   * Anything deeper than the limit will raise a warning and skip the entry.\n   * Set to `Infinity` to remove the limitation.\n   */\n  maxDepth?: number\n\n  /**\n   * When extracting, force all created files and directories, and all\n   * implicitly created directories, to be owned by the specified user id,\n   * regardless of the `uid` field in the archive.\n   *\n   * Cannot be used along with `preserveOwner`. Requires also setting the `gid`\n   * option.\n   */\n  uid?: number\n\n  /**\n   * When extracting, force all created files and directories, and all\n   * implicitly created directories, to be owned by the specified group id,\n   * regardless of the `gid` field in the archive.\n   *\n   * Cannot be used along with `preserveOwner`. Requires also setting the `uid`\n   * option.\n   */\n  gid?: number\n\n  /**\n   * When extracting, provide a function that takes an `entry` object, and\n   * returns a stream, or any falsey value. If a stream is provided, then that\n   * stream's data will be written instead of the contents of the archive\n   * entry. If a falsey value is provided, then the entry is written to disk as\n   * normal.\n   *\n   * To exclude items from extraction, use the `filter` option.\n   *\n   * Note that using an asynchronous stream type with the `transform` option\n   * will cause undefined behavior in synchronous extractions.\n   * [MiniPass](http://npm.im/minipass)-based streams are designed for this use\n   * case.\n   */\n  transform?: (entry: ReadEntry) => any\n\n  /**\n   * Call `chmod()` to ensure that extracted files match the entry's mode\n   * field. Without this field set, all mode fields in archive entries are a\n   * best effort attempt only.\n   *\n   * Setting this necessitates a call to the deprecated `process.umask()`\n   * method to determine the default umask value, unless a `processUmask`\n   * config is provided as well.\n   *\n   * If not set, tar will attempt to create file system entries with whatever\n   * mode is provided, and let the implicit process `umask` apply normally, but\n   * if a file already exists to be written to, then its existing mode will not\n   * be modified.\n   *\n   * When setting `chmod: true`, it is highly recommend to set the\n   * {@link TarOptions#processUmask} option as well, to avoid the call to the\n   * deprecated (and thread-unsafe) `process.umask()` method.\n   */\n  chmod?: boolean\n\n  /**\n   * When setting the {@link TarOptions#chmod} option to `true`, you may\n   * provide a value here to avoid having to call the deprecated and\n   * thread-unsafe `process.umask()` method.\n   *\n   * This has no effect with `chmod` is not set to true, as mode values are not\n   * set explicitly anyway. If `chmod` is set to `true`, and a value is not\n   * provided here, then `process.umask()` must be called, which will result in\n   * deprecation warnings.\n   *\n   * The most common values for this are `0o22` (resulting in directories\n   * created with mode `0o755` and files with `0o644` by default) and `0o2`\n   * (resulting in directores created with mode `0o775` and files `0o664`, so\n   * they are group-writable).\n   */\n  processUmask?: number\n\n  //////////////////////////\n  // archive creation options\n\n  /**\n   * When parsing/listing archives, `entry` streams are by default resumed\n   * (set into \"flowing\" mode) immediately after the call to `onReadEntry()`.\n   * Set `noResume: true` to suppress this behavior.\n   *\n   * Note that when this is set, the stream will never complete until the\n   * data is consumed somehow.\n   *\n   * Set automatically in extract operations, since the entry is piped to\n   * a file system entry right away. Only relevant when parsing.\n   */\n  noResume?: boolean\n\n  /**\n   * When creating, updating, or replacing within archives, this method will\n   * be called with each WriteEntry that is created.\n   */\n  onWriteEntry?: (entry: WriteEntry) => any\n\n  /**\n   * When extracting or listing archives, this method will be called with\n   * each entry that is not excluded by a `filter`.\n   *\n   * Important when listing archives synchronously from a file, because there\n   * is otherwise no way to interact with the data!\n   */\n  onReadEntry?: (entry: ReadEntry) => any\n\n  /**\n   * Pack the targets of symbolic links rather than the link itself.\n   */\n  follow?: boolean\n\n  /**\n   * When creating archives, omit any metadata that is system-specific:\n   * `ctime`, `atime`, `uid`, `gid`, `uname`, `gname`, `dev`, `ino`, and\n   * `nlink`. Note that `mtime` is still included, because this is necessary\n   * for other time-based operations such as `tar.update`. Additionally, `mode`\n   * is set to a \"reasonable default\" for mose unix systems, based on an\n   * effective `umask` of `0o22`.\n   *\n   * This also defaults the `portable` option in the gzip configs when creating\n   * a compressed archive, in order to produce deterministic archives that are\n   * not operating-system specific.\n   */\n  portable?: boolean\n\n  /**\n   * When creating archives, do not recursively archive the contents of\n   * directories. By default, archiving a directory archives all of its\n   * contents as well.\n   */\n  noDirRecurse?: boolean\n\n  /**\n   * Suppress Pax extended headers when creating archives. Note that this means\n   * long paths and linkpaths will be truncated, and large or negative numeric\n   * values may be interpreted incorrectly.\n   */\n  noPax?: boolean\n\n  /**\n   * Set to a `Date` object to force a specific `mtime` value for everything\n   * written to an archive.\n   *\n   * This is useful when creating archives that are intended to be\n   * deterministic based on their contents, irrespective of the file's last\n   * modification time.\n   *\n   * Overridden by `noMtime`.\n   */\n  mtime?: Date\n\n  /**\n   * A path portion to prefix onto the entries added to an archive.\n   */\n  prefix?: string\n\n  /**\n   * The mode to set on any created file archive, defaults to 0o666\n   * masked by the process umask, often resulting in 0o644.\n   *\n   * This does *not* affect the mode fields of individual entries, or the\n   * mode status of extracted entries on the filesystem.\n   */\n  mode?: number\n\n  //////////////////////////\n  // internal options\n\n  /**\n   * A cache of mtime values, to avoid having to stat the same file repeatedly.\n   *\n   * @internal\n   */\n  mtimeCache?: Map<string, Date>\n\n  /**\n   * maximum buffer size for `fs.read()` operations.\n   *\n   * @internal\n   */\n  maxReadSize?: number\n\n  /**\n   * Filter modes of entries being unpacked, like `process.umask()`\n   *\n   * @internal\n   */\n  umask?: number\n\n  /**\n   * Default mode for directories. Used for all implicitly created directories,\n   * and any directories in the archive that do not have a mode field.\n   *\n   * @internal\n   */\n  dmode?: number\n\n  /**\n   * default mode for files\n   *\n   * @internal\n   */\n  fmode?: number\n\n  /**\n   * Map that tracks which directories already exist, for extraction\n   *\n   * @internal\n   */\n  dirCache?: Map<string, boolean>\n  /**\n   * maximum supported size of meta entries. Defaults to 1MB\n   *\n   * @internal\n   */\n  maxMetaEntrySize?: number\n\n  /**\n   * A Map object containing the device and inode value for any file whose\n   * `nlink` value is greater than 1, to identify hard links when creating\n   * archives.\n   *\n   * @internal\n   */\n  linkCache?: Map<LinkCacheKey, string>\n\n  /**\n   * A map object containing the results of `fs.readdir()` calls.\n   *\n   * @internal\n   */\n  readdirCache?: Map<string, string[]>\n\n  /**\n   * A cache of all `lstat` results, for use in creating archives.\n   *\n   * @internal\n   */\n  statCache?: Map<string, Stats>\n\n  /**\n   * Number of concurrent jobs to run when creating archives.\n   *\n   * Defaults to 4.\n   *\n   * @internal\n   */\n  jobs?: number\n\n  /**\n   * Automatically set to true on Windows systems.\n   *\n   * When extracting, causes behavior where filenames containing `<|>?:`\n   * characters are converted to windows-compatible escape sequences in the\n   * created filesystem entries.\n   *\n   * When packing, causes behavior where paths replace `\\` with `/`, and\n   * filenames containing the windows-compatible escaped forms of `<|>?:` are\n   * converted to actual `<|>?:` characters in the archive.\n   *\n   * @internal\n   */\n  win32?: boolean\n\n  /**\n   * For `WriteEntry` objects, the absolute path to the entry on the\n   * filesystem. By default, this is `resolve(cwd, entry.path)`, but it can be\n   * overridden explicitly.\n   *\n   * @internal\n   */\n  absolute?: string\n\n  /**\n   * Used with Parser stream interface, to attach and take over when the\n   * stream is completely parsed. If this is set, then the prefinish,\n   * finish, and end events will not fire, and are the responsibility of\n   * the ondone method to emit properly.\n   *\n   * @internal\n   */\n  ondone?: () => void\n\n  /**\n   * Mostly for testing, but potentially useful in some cases.\n   * Forcibly trigger a chown on every entry, no matter what.\n   */\n  forceChown?: boolean\n\n  /**\n   * ambiguous deprecated name for {@link onReadEntry}\n   *\n   * @deprecated\n   */\n  onentry?: (entry: ReadEntry) => any\n}\n\nexport type TarOptionsSync = TarOptions & { sync: true }\nexport type TarOptionsAsync = TarOptions & { sync?: false }\nexport type TarOptionsFile = TarOptions & { file: string }\nexport type TarOptionsNoFile = TarOptions & { file?: undefined }\nexport type TarOptionsSyncFile = TarOptionsSync & TarOptionsFile\nexport type TarOptionsAsyncFile = TarOptionsAsync & TarOptionsFile\nexport type TarOptionsSyncNoFile = TarOptionsSync & TarOptionsNoFile\nexport type TarOptionsAsyncNoFile = TarOptionsAsync & TarOptionsNoFile\n\nexport type LinkCacheKey = `${number}:${number}`\n\nexport interface TarOptionsWithAliases extends TarOptions {\n  /**\n   * The effective current working directory for this tar command\n   */\n  C?: TarOptions['cwd']\n  /**\n   * The tar file to be read and/or written. When this is set, a stream\n   * is not returned. Asynchronous commands will return a promise indicating\n   * when the operation is completed, and synchronous commands will return\n   * immediately.\n   */\n  f?: TarOptions['file']\n  /**\n   * When creating a tar archive, this can be used to compress it as well.\n   * Set to `true` to use the default gzip options, or customize them as\n   * needed.\n   *\n   * When reading, if this is unset, then the compression status will be\n   * inferred from the archive data. This is generally best, unless you are\n   * sure of the compression settings in use to create the archive, and want to\n   * fail if the archive doesn't match expectations.\n   */\n  z?: TarOptions['gzip']\n  /**\n   * When creating archives, preserve absolute and `..` paths in the archive,\n   * rather than sanitizing them under the cwd.\n   *\n   * When extracting, allow absolute paths, paths containing `..`, and\n   * extracting through symbolic links. By default, the root `/` is stripped\n   * from absolute paths (eg, turning `/x/y/z` into `x/y/z`), paths containing\n   * `..` are not extracted, and any file whose location would be modified by a\n   * symbolic link is not extracted.\n   *\n   * **WARNING** This is almost always unsafe, and must NEVER be used on\n   * archives from untrusted sources, such as user input, and every entry must\n   * be validated to ensure it is safe to write. Even if the input is not\n   * malicious, mistakes can cause a lot of damage!\n   */\n  P?: TarOptions['preservePaths']\n  /**\n   * When extracting, unlink files before creating them. Without this option,\n   * tar overwrites existing files, which preserves existing hardlinks. With\n   * this option, existing hardlinks will be broken, as will any symlink that\n   * would affect the location of an extracted file.\n   */\n  U?: TarOptions['unlink']\n  /**\n   * When extracting, strip the specified number of path portions from the\n   * entry path. For example, with `{strip: 2}`, the entry `a/b/c/d` would be\n   * extracted to `{cwd}/c/d`.\n   */\n  'strip-components'?: TarOptions['strip']\n  /**\n   * When extracting, strip the specified number of path portions from the\n   * entry path. For example, with `{strip: 2}`, the entry `a/b/c/d` would be\n   * extracted to `{cwd}/c/d`.\n   */\n  stripComponents?: TarOptions['strip']\n  /**\n   * When extracting, keep the existing file on disk if it's newer than the\n   * file in the archive.\n   */\n  'keep-newer'?: TarOptions['newer']\n  /**\n   * When extracting, keep the existing file on disk if it's newer than the\n   * file in the archive.\n   */\n  keepNewer?: TarOptions['newer']\n  /**\n   * When extracting, keep the existing file on disk if it's newer than the\n   * file in the archive.\n   */\n  'keep-newer-files'?: TarOptions['newer']\n  /**\n   * When extracting, keep the existing file on disk if it's newer than the\n   * file in the archive.\n   */\n  keepNewerFiles?: TarOptions['newer']\n  /**\n   * When extracting, do not overwrite existing files at all.\n   */\n  k?: TarOptions['keep']\n  /**\n   * When extracting, do not overwrite existing files at all.\n   */\n  'keep-existing'?: TarOptions['keep']\n  /**\n   * When extracting, do not overwrite existing files at all.\n   */\n  keepExisting?: TarOptions['keep']\n  /**\n   * When extracting, do not set the `mtime` value for extracted entries to\n   * match the `mtime` in the archive.\n   *\n   * When creating archives, do not store the `mtime` value in the entry. Note\n   * that this prevents properly using other mtime-based features (such as\n   * `tar.update` or the `newer` option) with the resulting archive.\n   */\n  m?: TarOptions['noMtime']\n  /**\n   * When extracting, do not set the `mtime` value for extracted entries to\n   * match the `mtime` in the archive.\n   *\n   * When creating archives, do not store the `mtime` value in the entry. Note\n   * that this prevents properly using other mtime-based features (such as\n   * `tar.update` or the `newer` option) with the resulting archive.\n   */\n  'no-mtime'?: TarOptions['noMtime']\n  /**\n   * When extracting, set the `uid` and `gid` of extracted entries to the `uid`\n   * and `gid` fields in the archive. Defaults to true when run as root, and\n   * false otherwise.\n   *\n   * If false, then files and directories will be set with the owner and group\n   * of the user running the process. This is similar to `-p` in `tar(1)`, but\n   * ACLs and other system-specific data is never unpacked in this\n   * implementation, and modes are set by default already.\n   */\n  p?: TarOptions['preserveOwner']\n  /**\n   * Pack the targets of symbolic links rather than the link itself.\n   */\n  L?: TarOptions['follow']\n  /**\n   * Pack the targets of symbolic links rather than the link itself.\n   */\n  h?: TarOptions['follow']\n\n  /**\n   * Deprecated option. Set explicitly false to set `chmod: true`. Ignored\n   * if {@link TarOptions#chmod} is set to any boolean value.\n   *\n   * @deprecated\n   */\n  noChmod?: boolean\n}\n\nexport type TarOptionsWithAliasesSync = TarOptionsWithAliases & {\n  sync: true\n}\nexport type TarOptionsWithAliasesAsync = TarOptionsWithAliases & {\n  sync?: false\n}\nexport type TarOptionsWithAliasesFile =\n  | (TarOptionsWithAliases & {\n      file: string\n    })\n  | (TarOptionsWithAliases & { f: string })\nexport type TarOptionsWithAliasesSyncFile =\n  TarOptionsWithAliasesSync & TarOptionsWithAliasesFile\nexport type TarOptionsWithAliasesAsyncFile =\n  TarOptionsWithAliasesAsync & TarOptionsWithAliasesFile\n\nexport type TarOptionsWithAliasesNoFile = TarOptionsWithAliases & {\n  f?: undefined\n  file?: undefined\n}\n\nexport type TarOptionsWithAliasesSyncNoFile =\n  TarOptionsWithAliasesSync & TarOptionsWithAliasesNoFile\nexport type TarOptionsWithAliasesAsyncNoFile =\n  TarOptionsWithAliasesAsync & TarOptionsWithAliasesNoFile\n\nexport const isSyncFile = <O extends TarOptions>(\n  o: O,\n): o is O & TarOptionsSyncFile => !!o.sync && !!o.file\nexport const isAsyncFile = <O extends TarOptions>(\n  o: O,\n): o is O & TarOptionsAsyncFile => !o.sync && !!o.file\nexport const isSyncNoFile = <O extends TarOptions>(\n  o: O,\n): o is O & TarOptionsSyncNoFile => !!o.sync && !o.file\nexport const isAsyncNoFile = <O extends TarOptions>(\n  o: O,\n): o is O & TarOptionsAsyncNoFile => !o.sync && !o.file\nexport const isSync = <O extends TarOptions>(\n  o: O,\n): o is O & TarOptionsSync => !!o.sync\nexport const isAsync = <O extends TarOptions>(\n  o: O,\n): o is O & TarOptionsAsync => !o.sync\nexport const isFile = <O extends TarOptions>(\n  o: O,\n): o is O & TarOptionsFile => !!o.file\nexport const isNoFile = <O extends TarOptions>(\n  o: O,\n): o is O & TarOptionsNoFile => !o.file\n\nconst dealiasKey = (\n  k: keyof TarOptionsWithAliases,\n): keyof TarOptions => {\n  const d = argmap.get(k)\n  if (d) return d\n  return k as keyof TarOptions\n}\n\nexport const dealias = (\n  opt: TarOptionsWithAliases = {},\n): TarOptions => {\n  if (!opt) return {}\n  const result: Record<string, any> = {}\n  for (const [key, v] of Object.entries(opt) as [\n    keyof TarOptionsWithAliases,\n    any,\n  ][]) {\n    // TS doesn't know that aliases are going to always be the same type\n    const k = dealiasKey(key)\n    result[k] = v\n  }\n  // affordance for deprecated noChmod -> chmod\n  if (result.chmod === undefined && result.noChmod === false) {\n    result.chmod = true\n  }\n  delete result.noChmod\n  return result as TarOptions\n}\n"]}