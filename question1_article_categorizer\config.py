"""
Configuration file for Article Categorizer project.
Contains all model configurations, paths, and hyperparameters.
"""

import os
from typing import Dict, List

# Project paths
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))
DATA_DIR = os.path.join(PROJECT_ROOT, "data")
MODELS_DIR = os.path.join(PROJECT_ROOT, "models")
NOTEBOOKS_DIR = os.path.join(PROJECT_ROOT, "notebooks")

# Dataset configuration
CATEGORIES = ["Tech", "Finance", "Healthcare", "Sports", "Politics", "Entertainment"]
CATEGORY_MAPPING = {cat: idx for idx, cat in enumerate(CATEGORIES)}
REVERSE_CATEGORY_MAPPING = {idx: cat for cat, idx in CATEGORY_MAPPING.items()}

# Model configurations
MODEL_CONFIGS = {
    "word2vec": {
        "name": "Word2Vec/GloVe",
        "vector_size": 300,
        "window": 5,
        "min_count": 1,
        "workers": 4,
        "epochs": 100,
        "model_path": os.path.join(MODELS_DIR, "word2vec_model.pkl")
    },
    "bert": {
        "name": "BERT",
        "model_name": "bert-base-uncased",
        "max_length": 512,
        "batch_size": 16,
        "model_path": os.path.join(MODELS_DIR, "bert_classifier.pkl")
    },
    "sentence_bert": {
        "name": "Sentence-BERT",
        "model_name": "all-MiniLM-L6-v2",
        "max_length": 512,
        "batch_size": 32,
        "model_path": os.path.join(MODELS_DIR, "sbert_classifier.pkl")
    },
    "openai": {
        "name": "OpenAI",
        "model_name": "text-embedding-ada-002",
        "api_key_env": "OPENAI_API_KEY",
        "max_tokens": 8191,
        "model_path": os.path.join(MODELS_DIR, "openai_classifier.pkl")
    }
}

# Training configuration
TRAINING_CONFIG = {
    "test_size": 0.2,
    "random_state": 42,
    "cv_folds": 5,
    "logistic_regression": {
        "max_iter": 1000,
        "random_state": 42,
        "solver": "lbfgs"
    }
}

# Evaluation metrics
METRICS = ["accuracy", "precision", "recall", "f1"]

# Web UI configuration
WEB_CONFIG = {
    "host": "0.0.0.0",
    "port": 8000,
    "debug": True,
    "cors_origins": ["http://localhost:3000", "http://127.0.0.1:3000"]
}

# Frontend configuration
FRONTEND_CONFIG = {
    "port": 3000,
    "api_base_url": "http://localhost:8000"
}

# Visualization configuration
VIZ_CONFIG = {
    "figsize": (12, 8),
    "colors": ["#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FFEAA7", "#DDA0DD"],
    "plot_style": "whitegrid"
}

# Environment variables
REQUIRED_ENV_VARS = ["OPENAI_API_KEY"]

def validate_environment():
    """Validate that all required environment variables are set."""
    missing_vars = []
    for var in REQUIRED_ENV_VARS:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"Warning: Missing environment variables: {missing_vars}")
        print("Please set these variables in your .env file or environment")
    
    return len(missing_vars) == 0

def create_directories():
    """Create necessary directories if they don't exist."""
    directories = [DATA_DIR, MODELS_DIR, NOTEBOOKS_DIR]
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
    print("Project directories created successfully")

if __name__ == "__main__":
    create_directories()
    validate_environment()
