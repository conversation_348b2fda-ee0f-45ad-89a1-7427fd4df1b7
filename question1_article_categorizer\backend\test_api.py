"""
Comprehensive test suite for the Article Categorizer API.
Tests all endpoints, models, and functionality.
"""

import requests
import json
import time
from typing import Dict, List

# Configuration
BASE_URL = "http://localhost:8000"
EXPECTED_MODELS = ["word2vec", "bert", "sentence_bert", "openai"]
EXPECTED_CATEGORIES = ["Tech", "Finance", "Healthcare", "Sports", "Politics", "Entertainment"]

# Test data samples
TEST_SAMPLES = [
    {
        "text": "Apple announced revolutionary new iPhone with advanced AI capabilities",
        "expected_category": "Tech"
    },
    {
        "text": "The Federal Reserve announced new interest rate policies to combat inflation",
        "expected_category": "Finance"
    },
    {
        "text": "New breakthrough in cancer treatment shows promising results in clinical trials",
        "expected_category": "Healthcare"
    },
    {
        "text": "Manchester United defeated Barcelona 3-1 in Champions League final",
        "expected_category": "Sports"
    },
    {
        "text": "President announces new economic policies in State of the Union address",
        "expected_category": "Politics"
    },
    {
        "text": "Hollywood stars gather for Academy Awards ceremony in Los Angeles",
        "expected_category": "Entertainment"
    }
]

class APITester:
    def __init__(self):
        self.passed_tests = 0
        self.failed_tests = 0
        self.results = []

    def log_result(self, test_name: str, passed: bool, message: str = ""):
        """Log test result."""
        status = "✅ PASS" if passed else "❌ FAIL"
        result = f"{status} - {test_name}"
        if message:
            result += f": {message}"
        
        print(result)
        self.results.append({
            "test": test_name,
            "passed": passed,
            "message": message
        })
        
        if passed:
            self.passed_tests += 1
        else:
            self.failed_tests += 1

    def test_health_check(self):
        """Test health check endpoint."""
        try:
            response = requests.get(f"{BASE_URL}/")
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "healthy":
                    self.log_result("Health Check", True)
                else:
                    self.log_result("Health Check", False, "Invalid response format")
            else:
                self.log_result("Health Check", False, f"Status code: {response.status_code}")
        except Exception as e:
            self.log_result("Health Check", False, str(e))

    def test_models_endpoint(self):
        """Test models endpoint."""
        try:
            response = requests.get(f"{BASE_URL}/models")
            if response.status_code == 200:
                data = response.json()
                available_models = data.get("available_models", [])
                
                # Check if all expected models are available
                missing_models = set(EXPECTED_MODELS) - set(available_models)
                if not missing_models:
                    self.log_result("Models Endpoint", True, f"All {len(EXPECTED_MODELS)} models available")
                else:
                    self.log_result("Models Endpoint", False, f"Missing models: {missing_models}")
            else:
                self.log_result("Models Endpoint", False, f"Status code: {response.status_code}")
        except Exception as e:
            self.log_result("Models Endpoint", False, str(e))

    def test_categories_endpoint(self):
        """Test categories endpoint."""
        try:
            response = requests.get(f"{BASE_URL}/categories")
            if response.status_code == 200:
                data = response.json()
                categories = data.get("categories", [])
                
                # Check if all expected categories are available
                missing_categories = set(EXPECTED_CATEGORIES) - set(categories)
                if not missing_categories:
                    self.log_result("Categories Endpoint", True, f"All {len(EXPECTED_CATEGORIES)} categories available")
                else:
                    self.log_result("Categories Endpoint", False, f"Missing categories: {missing_categories}")
            else:
                self.log_result("Categories Endpoint", False, f"Status code: {response.status_code}")
        except Exception as e:
            self.log_result("Categories Endpoint", False, str(e))

    def test_single_model_prediction(self, model_type: str):
        """Test single model prediction."""
        try:
            test_sample = TEST_SAMPLES[0]  # Use first test sample
            payload = {
                "text": test_sample["text"],
                "model_type": model_type
            }
            
            response = requests.post(f"{BASE_URL}/predict", json=payload)
            if response.status_code == 200:
                data = response.json()
                
                # Validate response structure
                required_fields = ["text", "predicted_category", "confidence"]
                missing_fields = [field for field in required_fields if field not in data]
                
                if not missing_fields:
                    confidence = data["confidence"]
                    if 0 <= confidence <= 1:
                        self.log_result(f"Single Model Prediction ({model_type})", True, 
                                      f"Category: {data['predicted_category']}, Confidence: {confidence:.3f}")
                    else:
                        self.log_result(f"Single Model Prediction ({model_type})", False, 
                                      f"Invalid confidence value: {confidence}")
                else:
                    self.log_result(f"Single Model Prediction ({model_type})", False, 
                                  f"Missing fields: {missing_fields}")
            else:
                self.log_result(f"Single Model Prediction ({model_type})", False, 
                              f"Status code: {response.status_code}")
        except Exception as e:
            self.log_result(f"Single Model Prediction ({model_type})", False, str(e))

    def test_multi_model_prediction(self):
        """Test multi-model prediction endpoint."""
        try:
            test_sample = TEST_SAMPLES[0]  # Use first test sample
            payload = {"text": test_sample["text"]}
            
            response = requests.post(f"{BASE_URL}/predict-all", json=payload)
            if response.status_code == 200:
                data = response.json()
                
                # Validate response structure
                if "text" in data and "results" in data:
                    results = data["results"]
                    
                    # Check if all models returned results
                    successful_models = []
                    failed_models = []
                    
                    for model in EXPECTED_MODELS:
                        if model in results:
                            model_result = results[model]
                            if "error" in model_result:
                                failed_models.append(f"{model}: {model_result['error']}")
                            elif all(field in model_result for field in ["predicted_category", "confidence", "probabilities"]):
                                successful_models.append(model)
                            else:
                                failed_models.append(f"{model}: Missing required fields")
                        else:
                            failed_models.append(f"{model}: Not in results")
                    
                    if not failed_models:
                        self.log_result("Multi-Model Prediction", True, 
                                      f"All {len(successful_models)} models successful")
                    else:
                        self.log_result("Multi-Model Prediction", False, 
                                      f"Failed models: {failed_models}")
                else:
                    self.log_result("Multi-Model Prediction", False, "Invalid response structure")
            else:
                self.log_result("Multi-Model Prediction", False, f"Status code: {response.status_code}")
        except Exception as e:
            self.log_result("Multi-Model Prediction", False, str(e))

    def test_prediction_consistency(self):
        """Test prediction consistency across multiple calls."""
        try:
            test_sample = TEST_SAMPLES[0]
            payload = {"text": test_sample["text"]}
            
            # Make multiple requests
            responses = []
            for i in range(3):
                response = requests.post(f"{BASE_URL}/predict-all", json=payload)
                if response.status_code == 200:
                    responses.append(response.json())
                time.sleep(0.5)  # Small delay between requests
            
            if len(responses) == 3:
                # Check if predictions are consistent
                consistent = True
                for model in EXPECTED_MODELS:
                    predictions = []
                    for resp in responses:
                        if model in resp["results"] and "predicted_category" in resp["results"][model]:
                            predictions.append(resp["results"][model]["predicted_category"])
                    
                    if len(set(predictions)) > 1:  # More than one unique prediction
                        consistent = False
                        break
                
                self.log_result("Prediction Consistency", consistent, 
                              "Predictions consistent across multiple calls" if consistent 
                              else "Predictions vary across calls")
            else:
                self.log_result("Prediction Consistency", False, "Failed to get multiple responses")
        except Exception as e:
            self.log_result("Prediction Consistency", False, str(e))

    def test_category_coverage(self):
        """Test if models can predict all categories."""
        category_predictions = {category: False for category in EXPECTED_CATEGORIES}
        
        try:
            for sample in TEST_SAMPLES:
                payload = {"text": sample["text"]}
                response = requests.post(f"{BASE_URL}/predict-all", json=payload)
                
                if response.status_code == 200:
                    data = response.json()
                    results = data.get("results", {})
                    
                    # Check predictions from all models
                    for model_result in results.values():
                        if "predicted_category" in model_result:
                            predicted_category = model_result["predicted_category"]
                            if predicted_category in category_predictions:
                                category_predictions[predicted_category] = True
                
                time.sleep(0.3)  # Small delay between requests
            
            covered_categories = sum(category_predictions.values())
            total_categories = len(EXPECTED_CATEGORIES)
            
            if covered_categories == total_categories:
                self.log_result("Category Coverage", True, f"All {total_categories} categories predicted")
            else:
                uncovered = [cat for cat, covered in category_predictions.items() if not covered]
                self.log_result("Category Coverage", False, f"Uncovered categories: {uncovered}")
                
        except Exception as e:
            self.log_result("Category Coverage", False, str(e))

    def run_all_tests(self):
        """Run all tests."""
        print("🧪 Starting API Test Suite")
        print("=" * 50)
        
        # Basic endpoint tests
        self.test_health_check()
        self.test_models_endpoint()
        self.test_categories_endpoint()
        
        # Model-specific tests
        for model in EXPECTED_MODELS:
            self.test_single_model_prediction(model)
        
        # Advanced tests
        self.test_multi_model_prediction()
        self.test_prediction_consistency()
        self.test_category_coverage()
        
        # Summary
        print("\n" + "=" * 50)
        print("📊 Test Summary")
        print(f"✅ Passed: {self.passed_tests}")
        print(f"❌ Failed: {self.failed_tests}")
        print(f"📈 Success Rate: {self.passed_tests/(self.passed_tests + self.failed_tests)*100:.1f}%")
        
        if self.failed_tests == 0:
            print("\n🎉 All tests passed! The API is working perfectly.")
        else:
            print(f"\n⚠️  {self.failed_tests} test(s) failed. Please review the issues above.")

if __name__ == "__main__":
    tester = APITester()
    tester.run_all_tests()
