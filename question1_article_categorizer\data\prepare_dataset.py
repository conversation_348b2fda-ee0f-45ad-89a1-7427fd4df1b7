"""
Data preparation script for article categorization dataset.
Creates sample articles for 6 categories: Tech, Finance, Healthcare, Sports, Politics, Entertainment
"""

import pandas as pd
import os
import json
from typing import List, Dict

# Sample articles for each category
SAMPLE_ARTICLES = {
    "Tech": [
        "Apple unveiled its latest iPhone with advanced AI capabilities and improved camera technology. The new device features a faster processor and enhanced battery life.",
        "Google's new AI model demonstrates breakthrough performance in natural language processing tasks, outperforming previous models by significant margins.",
        "Microsoft announces major updates to its cloud computing platform, introducing new machine learning tools for developers and enterprises.",
        "Tesla's latest software update includes enhanced autopilot features and improved energy efficiency for electric vehicles.",
        "Meta introduces new virtual reality headset with improved resolution and reduced latency for immersive gaming experiences.",
        "Amazon Web Services launches new quantum computing service for researchers and enterprises exploring quantum algorithms.",
        "NVIDIA's latest graphics cards deliver unprecedented performance for gaming and AI workloads with advanced ray tracing capabilities.",
        "SpaceX successfully launches another batch of Starlink satellites, expanding global internet coverage to remote areas.",
        "Intel announces breakthrough in semiconductor manufacturing with new 3nm process technology for next-generation processors.",
        "OpenAI releases new language model with improved reasoning capabilities and reduced hallucination rates."
    ],
    "Finance": [
        "Federal Reserve announces interest rate decision amid inflation concerns and economic uncertainty in global markets.",
        "Stock market reaches new highs as technology companies report strong quarterly earnings and revenue growth.",
        "Cryptocurrency market experiences volatility as regulatory frameworks evolve across different countries and jurisdictions.",
        "Major banks report increased profits from lending activities and improved credit quality in consumer portfolios.",
        "Investment firms recommend diversified portfolios as economic indicators show mixed signals for future growth.",
        "Real estate market shows signs of cooling as mortgage rates increase and housing inventory remains limited.",
        "Gold prices surge amid geopolitical tensions and concerns about currency devaluation in emerging markets.",
        "Corporate earnings season reveals strong performance in technology sector while retail faces ongoing challenges.",
        "Central banks coordinate monetary policy responses to address global inflation and supply chain disruptions.",
        "Venture capital funding reaches record levels as startups in AI and clean energy attract significant investments."
    ],
    "Healthcare": [
        "New cancer treatment shows promising results in clinical trials, offering hope for patients with advanced stages.",
        "COVID-19 vaccine effectiveness remains high against new variants according to latest epidemiological studies.",
        "Mental health awareness campaigns highlight importance of early intervention and accessible treatment options.",
        "Medical researchers develop innovative gene therapy approach for treating rare genetic disorders in children.",
        "Healthcare systems implement AI-powered diagnostic tools to improve accuracy and reduce physician workload.",
        "Pharmaceutical companies collaborate on drug development for Alzheimer's disease using novel therapeutic targets.",
        "Telemedicine adoption continues to grow as patients and providers embrace remote healthcare delivery models.",
        "Public health officials recommend updated vaccination schedules based on latest immunological research findings.",
        "Medical device manufacturers introduce wearable technology for continuous monitoring of chronic conditions.",
        "Healthcare policy reforms aim to improve access and affordability while maintaining quality of care standards."
    ],
    "Sports": [
        "World Cup final draws record television audience as two powerhouse teams compete for championship title.",
        "Olympic Games preparation intensifies as athletes train for upcoming competition in various sporting disciplines.",
        "Professional basketball season begins with new rules and expanded playoff format to enhance fan engagement.",
        "Tennis tournament features exciting matches between top-ranked players competing for prestigious grand slam title.",
        "Football season kicks off with high expectations as teams showcase new strategies and talented rookie players.",
        "Baseball playoffs deliver thrilling games with unexpected upsets and outstanding individual performances.",
        "Swimming championships break multiple world records as athletes push boundaries of human performance.",
        "Golf tournament attracts international field of competitors vying for major championship and prize money.",
        "Marathon event celebrates community participation while elite runners compete for course records and prizes.",
        "Winter sports season begins with skiing and snowboarding competitions in mountain resort destinations."
    ],
    "Politics": [
        "Presidential election campaign intensifies as candidates present policy platforms on key national issues.",
        "Congressional hearing examines government response to recent crisis and accountability measures for officials.",
        "International summit addresses climate change policies and cooperation between developed and developing nations.",
        "Supreme Court decision impacts constitutional interpretation and sets precedent for future legal cases.",
        "Local elections demonstrate voter engagement in municipal governance and community development initiatives.",
        "Foreign policy negotiations continue as diplomats work to resolve territorial disputes through peaceful dialogue.",
        "Legislative session focuses on healthcare reform, education funding, and infrastructure investment priorities.",
        "Political parties prepare for upcoming elections while addressing internal divisions and leadership changes.",
        "Government budget proposal allocates resources for defense, social programs, and economic development projects.",
        "International trade agreements face scrutiny as lawmakers debate economic benefits and potential drawbacks."
    ],
    "Entertainment": [
        "Hollywood blockbuster breaks box office records with spectacular visual effects and compelling storyline.",
        "Music festival lineup announced featuring diverse artists from multiple genres and international performers.",
        "Television series finale draws massive viewership as fans celebrate conclusion of beloved long-running show.",
        "Celebrity couple announces engagement at glamorous red carpet event attended by industry professionals.",
        "Streaming platform launches original content series with star-studded cast and high production values.",
        "Award ceremony honors outstanding achievements in film, television, and music entertainment industries.",
        "Concert tour sells out venues across multiple cities as popular artist performs hits from latest album.",
        "Movie premiere generates excitement among fans and critics anticipating highly anticipated sequel release.",
        "Fashion week showcases latest designer collections with innovative styles and sustainable materials.",
        "Gaming convention reveals upcoming video game releases with advanced graphics and immersive gameplay features."
    ]
}

def create_dataset() -> pd.DataFrame:
    """Create a balanced dataset with articles from all categories."""
    data = []
    
    for category, articles in SAMPLE_ARTICLES.items():
        for article in articles:
            data.append({
                'text': article,
                'category': category,
                'length': len(article.split())
            })
    
    df = pd.DataFrame(data)
    return df

def save_dataset(df: pd.DataFrame, data_dir: str = "data"):
    """Save dataset in multiple formats."""
    os.makedirs(data_dir, exist_ok=True)
    
    # Save as CSV
    df.to_csv(os.path.join(data_dir, "articles_dataset.csv"), index=False)
    
    # Save as JSON
    df.to_json(os.path.join(data_dir, "articles_dataset.json"), orient="records", indent=2)
    
    # Create train/test split
    from sklearn.model_selection import train_test_split
    
    train_df, test_df = train_test_split(df, test_size=0.2, stratify=df['category'], random_state=42)
    
    train_df.to_csv(os.path.join(data_dir, "train_dataset.csv"), index=False)
    test_df.to_csv(os.path.join(data_dir, "test_dataset.csv"), index=False)
    
    # Save category mapping
    categories = df['category'].unique().tolist()
    category_mapping = {cat: idx for idx, cat in enumerate(categories)}
    
    with open(os.path.join(data_dir, "category_mapping.json"), "w") as f:
        json.dump(category_mapping, f, indent=2)
    
    print(f"Dataset created with {len(df)} articles")
    print(f"Categories: {categories}")
    print(f"Distribution:\n{df['category'].value_counts()}")
    print(f"Train set: {len(train_df)} articles")
    print(f"Test set: {len(test_df)} articles")

if __name__ == "__main__":
    # Create dataset
    df = create_dataset()
    
    # Save dataset
    save_dataset(df)
    
    print("Dataset preparation completed!")
