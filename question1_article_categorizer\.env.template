# Environment variables for Article Categorizer

# OpenAI API Key (required for OpenAI embeddings)
OPENAI_API_KEY=your_openai_api_key_here

# Optional: Custom model paths
# WORD2VEC_MODEL_PATH=path/to/custom/word2vec/model
# BERT_MODEL_PATH=path/to/custom/bert/model

# Optional: Database configuration (if using external database)
# DATABASE_URL=sqlite:///./articles.db

# Optional: Logging configuration
# LOG_LEVEL=INFO
# LOG_FILE=logs/app.log
