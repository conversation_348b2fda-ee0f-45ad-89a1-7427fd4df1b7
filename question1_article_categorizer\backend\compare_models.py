"""
Comprehensive model comparison script for all embedding approaches.
"""

import pandas as pd
import numpy as np
# import matplotlib.pyplot as plt
# import seaborn as sns
import os
import sys
import json
from datetime import datetime

# Add current directory to path for imports
sys.path.append(os.path.dirname(__file__))

from classifier import ArticleClassifier, MultiModelClassifier
from config import create_directories

def load_test_data():
    """Load test dataset."""
    data_dir = os.path.join(os.path.dirname(__file__), '..', 'data')
    test_file = os.path.join(data_dir, 'test_dataset.csv')
    
    if not os.path.exists(test_file):
        raise FileNotFoundError(f"Test dataset not found at {test_file}")
    
    df = pd.read_csv(test_file)
    return df['text'].tolist(), df['category'].tolist()

def compare_all_models():
    """Compare all embedding models on the same test set."""
    
    print("🚀 Starting comprehensive model comparison...")
    print("=" * 60)
    
    # Load test data
    try:
        texts, true_labels = load_test_data()
        print(f"✅ Loaded {len(texts)} test samples")
    except Exception as e:
        print(f"❌ Error loading test data: {e}")
        return
    
    # Model types to compare
    model_types = ['word2vec', 'bert', 'sentence_bert', 'openai']
    results = {}
    
    for model_type in model_types:
        print(f"\n🔄 Testing {model_type.upper()} model...")
        print("-" * 40)
        
        try:
            # Initialize classifier
            classifier = ArticleClassifier(model_type)
            
            # Load trained model
            if not classifier.load_model():
                print(f"❌ Failed to load {model_type} model")
                continue
            
            # Make predictions
            predictions = classifier.predict_with_probabilities(texts)
            
            if not predictions:
                print(f"❌ No predictions generated for {model_type}")
                continue
            
            # Extract predicted categories and confidences
            predicted_labels = [pred['predicted_category'] for pred in predictions]
            confidences = [pred['confidence'] for pred in predictions]
            
            # Calculate metrics
            from sklearn.metrics import accuracy_score, precision_recall_fscore_support, classification_report
            
            accuracy = accuracy_score(true_labels, predicted_labels)
            precision, recall, f1, _ = precision_recall_fscore_support(
                true_labels, predicted_labels, average='weighted'
            )
            
            # Store results
            results[model_type] = {
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1_score': f1,
                'avg_confidence': np.mean(confidences),
                'predictions': predictions[:5]  # Store first 5 for examples
            }
            
            print(f"✅ {model_type.upper()} Results:")
            print(f"   Accuracy: {accuracy:.4f}")
            print(f"   Precision: {precision:.4f}")
            print(f"   Recall: {recall:.4f}")
            print(f"   F1-Score: {f1:.4f}")
            print(f"   Avg Confidence: {np.mean(confidences):.4f}")
            
        except Exception as e:
            print(f"❌ Error testing {model_type}: {e}")
            results[model_type] = {'error': str(e)}
    
    # Print comparison summary
    print("\n" + "=" * 60)
    print("📊 FINAL COMPARISON SUMMARY")
    print("=" * 60)
    
    # Create comparison table
    comparison_data = []
    for model_type, result in results.items():
        if 'error' not in result:
            comparison_data.append({
                'Model': model_type.upper(),
                'Accuracy': f"{result['accuracy']:.4f}",
                'Precision': f"{result['precision']:.4f}",
                'Recall': f"{result['recall']:.4f}",
                'F1-Score': f"{result['f1_score']:.4f}",
                'Avg Confidence': f"{result['avg_confidence']:.4f}"
            })
    
    if comparison_data:
        df_comparison = pd.DataFrame(comparison_data)
        print(df_comparison.to_string(index=False))
        
        # Find best model
        best_model = max(comparison_data, key=lambda x: float(x['F1-Score']))
        print(f"\n🏆 Best performing model: {best_model['Model']} (F1-Score: {best_model['F1-Score']})")
    
    # Save results
    results_dir = os.path.join(os.path.dirname(__file__), '..', 'results')
    os.makedirs(results_dir, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = os.path.join(results_dir, f'model_comparison_{timestamp}.json')
    
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n💾 Results saved to: {results_file}")
    
    return results

def test_multi_model_classifier():
    """Test the multi-model classifier that uses all models simultaneously."""
    
    print("\n🔄 Testing Multi-Model Classifier...")
    print("=" * 60)
    
    try:
        # Load test data
        texts, true_labels = load_test_data()
        test_texts = texts[:10]  # Test with first 10 samples
        
        # Initialize multi-model classifier
        multi_classifier = MultiModelClassifier()
        
        # Get predictions from all models
        results = multi_classifier.predict_all_models(test_texts)
        
        print("✅ Multi-model predictions:")
        for i, (text, result) in enumerate(zip(test_texts, results)):
            print(f"\nSample {i+1}: {text[:100]}...")
            print(f"True label: {true_labels[i]}")
            
            for model_type, prediction in result.items():
                if 'error' not in prediction:
                    print(f"  {model_type}: {prediction['predicted_category']} ({prediction['confidence']:.3f})")
                else:
                    print(f"  {model_type}: ERROR - {prediction['error']}")
    
    except Exception as e:
        print(f"❌ Error in multi-model testing: {e}")

if __name__ == "__main__":
    # Ensure directories exist
    create_directories()
    
    # Run comprehensive comparison
    results = compare_all_models()
    
    # Test multi-model classifier
    test_multi_model_classifier()
    
    print("\n🎉 Model comparison completed!")
