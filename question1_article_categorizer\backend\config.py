"""
Configuration settings for the Article Categorizer backend.
"""

import os
from pathlib import Path

# Base directories
BASE_DIR = Path(__file__).parent
PROJECT_DIR = BASE_DIR.parent
DATA_DIR = PROJECT_DIR / "data"
MODELS_DIR = PROJECT_DIR / "models"

# Ensure directories exist
MODELS_DIR.mkdir(exist_ok=True)
DATA_DIR.mkdir(exist_ok=True)

# Model configurations
MODEL_CONFIGS = {
    'word2vec': {
        'name': 'Word2Vec (TF-IDF)',
        'description': 'TF-IDF vectorization with max_features=300',
        'model_file': 'word2vec_model.pkl',
        'classifier_file': 'word2vec_model_classifier.pkl',
        'label_encoder_file': 'word2vec_model_label_encoder.pkl',
        'model_path': str(MODELS_DIR / 'word2vec_model.pkl'),
        'max_features': 300,
        'vector_size': 300
    },
    'bert': {
        'name': 'BERT',
        'description': 'BERT base uncased with [CLS] token embeddings',
        'model_file': 'bert_classifier.pkl',
        'classifier_file': 'bert_classifier_classifier.pkl',
        'label_encoder_file': 'bert_classifier_label_encoder.pkl',
        'model_path': str(MODELS_DIR / 'bert_classifier.pkl'),
        'model_name': 'bert-base-uncased',
        'max_length': 512,
        'batch_size': 16
    },
    'sentence_bert': {
        'name': 'Sentence-BERT',
        'description': 'all-MiniLM-L6-v2 sentence embeddings',
        'model_file': 'sbert_classifier.pkl',
        'classifier_file': 'sbert_classifier_classifier.pkl',
        'label_encoder_file': 'sbert_classifier_label_encoder.pkl',
        'model_path': str(MODELS_DIR / 'sbert_classifier.pkl'),
        'model_name': 'all-MiniLM-L6-v2',
        'batch_size': 32
    },
    'openai': {
        'name': 'OpenAI',
        'description': 'text-embedding-ada-002 API embeddings',
        'model_file': 'openai_classifier.pkl',
        'classifier_file': 'openai_classifier_classifier.pkl',
        'label_encoder_file': 'openai_classifier_label_encoder.pkl',
        'model_path': str(MODELS_DIR / 'openai_classifier.pkl'),
        'model_name': 'text-embedding-ada-002',
        'embedding_dim': 1536
    }
}

# Categories
CATEGORIES = ['Tech', 'Finance', 'Healthcare', 'Sports', 'Politics', 'Entertainment']

# Training configuration
TRAINING_CONFIG = {
    'test_size': 0.2,
    'random_state': 42,
    'cv_folds': 5,
    'logistic_regression': {
        'max_iter': 1000,
        'random_state': 42
    }
}

# API settings
API_HOST = "0.0.0.0"
API_PORT = 8000
API_RELOAD = True

# CORS settings
CORS_ORIGINS = [
    "http://localhost:3000",
    "http://localhost:5173",
    "http://localhost:5174",
    "http://127.0.0.1:3000",
    "http://127.0.0.1:5173",
    "http://127.0.0.1:5174"
]

# Model loading settings
BATCH_SIZE = 32
MAX_LENGTH = 512
