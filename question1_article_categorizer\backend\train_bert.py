"""
Training script for BERT classifier.
"""

import pandas as pd
import os
import sys

# Add current directory to path for imports
sys.path.append(os.path.dirname(__file__))

from classifier import ArticleClassifier
from config import create_directories

def main():
    """Train BERT classifier."""
    print("Starting BERT classifier training...")
    
    # Create directories
    create_directories()
    
    # Load training data
    data_path = os.path.join(os.path.dirname(__file__), "..", "data", "train_dataset.csv")
    if not os.path.exists(data_path):
        print(f"Training data not found at {data_path}")
        print("Please run data/prepare_dataset.py first")
        return
    
    df = pd.read_csv(data_path)
    print(f"Loaded {len(df)} training samples")
    print(f"Categories: {df['category'].unique()}")
    print(f"Distribution:\n{df['category'].value_counts()}")
    
    # Initialize classifier
    classifier = ArticleClassifier("bert")
    
    # Train classifier
    try:
        results = classifier.train(df)
        
        # Save model
        classifier.save_model()
        
        # Print results
        print("\n" + "="*50)
        print("TRAINING RESULTS")
        print("="*50)
        print(f"Embedding Type: {results['embedding_type']}")
        print(f"Train Accuracy: {results['train_accuracy']:.4f}")
        print(f"Test Accuracy: {results['test_accuracy']:.4f}")
        print(f"CV Mean: {results['cv_mean']:.4f}")
        print(f"CV Std: {results['cv_std']:.4f}")
        print(f"Precision: {results['precision']:.4f}")
        print(f"Recall: {results['recall']:.4f}")
        print(f"F1 Score: {results['f1_score']:.4f}")
        
        # Test prediction
        print("\n" + "="*50)
        print("TESTING PREDICTIONS")
        print("="*50)
        
        test_texts = [
            "Apple releases new iPhone with advanced AI capabilities and improved camera technology.",
            "Stock market reaches new highs as technology companies report strong earnings.",
            "New cancer treatment shows promising results in clinical trials."
        ]
        
        predictions = classifier.predict_with_probabilities(test_texts)
        
        for pred in predictions:
            print(f"\nText: {pred['text'][:80]}...")
            print(f"Predicted: {pred['predicted_category']} (confidence: {pred['confidence']:.3f})")
            print("Probabilities:")
            for cat, prob in pred['probabilities'].items():
                print(f"  {cat}: {prob:.3f}")
        
        print("\nBERT classifier training completed successfully!")
        
    except Exception as e:
        print(f"Error during training: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
