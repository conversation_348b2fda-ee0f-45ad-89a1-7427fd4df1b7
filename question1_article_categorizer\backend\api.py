"""
FastAPI backend for article categorization.
"""

from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Any
import os
import sys
import uvicorn

# Add current directory to path
sys.path.append(os.path.dirname(__file__))

from classifier import ArticleClassifier

app = FastAPI(title="Article Categorizer API", version="1.0.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:5173", "http://localhost:5174"],  # React dev servers
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global classifiers
classifiers = {}

class PredictionRequest(BaseModel):
    text: str
    model_type: str = "bert"  # default to BERT

class PredictionResponse(BaseModel):
    text: str
    predicted_category: str
    confidence: float
    probabilities: Dict[str, float]
    model_type: str

class HealthResponse(BaseModel):
    status: str
    available_models: List[str]

@app.on_event("startup")
async def startup_event():
    """Load models on startup."""
    global classifiers

    # Set OpenAI API key from environment variable
    if not os.environ.get('OPENAI_API_KEY'):
        print("Warning: OPENAI_API_KEY environment variable not set. OpenAI model will not work.")
        print("Please set your OpenAI API key: export OPENAI_API_KEY='your-api-key-here'")

    # Available models
    available_models = ["word2vec", "bert", "sentence_bert", "openai"]
    
    for model_type in available_models:
        try:
            print(f"Loading {model_type} classifier...")
            classifier = ArticleClassifier(model_type)
            
            # Try to load the model
            try:
                classifier.load_model()
                classifiers[model_type] = classifier
                print(f"✓ {model_type} classifier loaded successfully")
            except Exception as e:
                print(f"✗ Failed to load {model_type} classifier: {e}")
                
        except Exception as e:
            print(f"✗ Error initializing {model_type} classifier: {e}")
    
    print(f"Loaded {len(classifiers)} classifiers: {list(classifiers.keys())}")

@app.get("/", response_model=HealthResponse)
async def health_check():
    """Health check endpoint."""
    return HealthResponse(
        status="healthy",
        available_models=list(classifiers.keys())
    )

@app.get("/models")
async def get_available_models():
    """Get list of available models."""
    return {
        "available_models": list(classifiers.keys()),
        "model_details": {
            "word2vec": "TF-IDF based word embeddings with document averaging",
            "bert": "BERT embeddings using [CLS] token",
            "sentence_bert": "Sentence-BERT embeddings for direct sentence representation",
            "openai": "OpenAI text-embedding-ada-002 API embeddings"
        }
    }

@app.post("/predict", response_model=PredictionResponse)
async def predict_category(request: PredictionRequest):
    """Predict article category."""
    
    if not request.text.strip():
        raise HTTPException(status_code=400, detail="Text cannot be empty")
    
    if request.model_type not in classifiers:
        available = list(classifiers.keys())
        raise HTTPException(
            status_code=400, 
            detail=f"Model '{request.model_type}' not available. Available models: {available}"
        )
    
    try:
        classifier = classifiers[request.model_type]
        
        # Get prediction with probabilities
        predictions = classifier.predict_with_probabilities([request.text])
        
        if not predictions:
            raise HTTPException(status_code=500, detail="Failed to generate prediction")
        
        prediction = predictions[0]
        
        return PredictionResponse(
            text=request.text,
            predicted_category=prediction['predicted_category'],
            confidence=prediction['confidence'],
            probabilities=prediction['probabilities'],
            model_type=request.model_type
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Prediction failed: {str(e)}")

@app.post("/predict/batch")
async def predict_batch(texts: List[str], model_type: str = "bert"):
    """Predict categories for multiple texts."""
    
    if not texts:
        raise HTTPException(status_code=400, detail="Text list cannot be empty")
    
    if model_type not in classifiers:
        available = list(classifiers.keys())
        raise HTTPException(
            status_code=400, 
            detail=f"Model '{model_type}' not available. Available models: {available}"
        )
    
    try:
        classifier = classifiers[model_type]
        predictions = classifier.predict_with_probabilities(texts)
        
        return {
            "model_type": model_type,
            "predictions": predictions
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Batch prediction failed: {str(e)}")

@app.post("/predict-all")
async def predict_all_models(request: dict):
    """Predict article category using all available models."""

    text = request.get('text', '').strip()
    if not text:
        raise HTTPException(status_code=400, detail="Text cannot be empty")

    try:
        results = {}

        for model_type, classifier in classifiers.items():
            try:
                # Get prediction with probabilities
                predictions = classifier.predict_with_probabilities([text])

                if predictions:
                    prediction = predictions[0]
                    results[model_type] = {
                        "predicted_category": prediction['predicted_category'],
                        "confidence": prediction['confidence'],
                        "probabilities": prediction['probabilities']
                    }
                else:
                    results[model_type] = {
                        "error": "Failed to generate prediction"
                    }

            except Exception as e:
                results[model_type] = {
                    "error": f"Prediction failed: {str(e)}"
                }

        return {
            "text": text,
            "results": results
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Multi-model prediction failed: {str(e)}")

@app.get("/categories")
async def get_categories():
    """Get available categories."""
    return {
        "categories": [
            "Tech",
            "Finance", 
            "Healthcare",
            "Sports",
            "Politics",
            "Entertainment"
        ],
        "descriptions": {
            "Tech": "Technology, gadgets, software, AI, and tech industry news",
            "Finance": "Stock market, economy, business, investments, and financial news",
            "Healthcare": "Medical research, treatments, health policy, and healthcare news",
            "Sports": "Sports events, athletes, teams, and sports industry news",
            "Politics": "Government, elections, policy, and political news",
            "Entertainment": "Movies, music, celebrities, and entertainment industry news"
        }
    }

if __name__ == "__main__":
    uvicorn.run(
        "api:app",
        host="0.0.0.0",
        port=8000,
        reload=True
    )
