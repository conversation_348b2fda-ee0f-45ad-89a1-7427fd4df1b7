"""
Classification module for article categorization.
Implements logistic regression classifiers for different embedding types.
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Optional
import pickle
import os
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import accuracy_score, precision_recall_fscore_support, classification_report, confusion_matrix
from sklearn.preprocessing import LabelEncoder
import joblib

from embeddings import get_embedding_model, BaseEmbedding
from config import M<PERSON>EL_CONFIGS, TRAINING_CONFIG, CATEGORIES, MODELS_DIR

class ArticleClassifier:
    """Article classifier using different embedding approaches."""
    
    def __init__(self, embedding_type: str):
        self.embedding_type = embedding_type
        self.embedding_model = get_embedding_model(embedding_type)
        self.classifier = LogisticRegression(**TRAINING_CONFIG['logistic_regression'])
        self.label_encoder = LabelEncoder()
        self.is_trained = False
        
        # Model paths
        self.embedding_model_path = MODEL_CONFIGS[embedding_type]['model_path']
        self.classifier_path = self.embedding_model_path.replace('.pkl', '_classifier.pkl')
        self.label_encoder_path = self.embedding_model_path.replace('.pkl', '_label_encoder.pkl')
    
    def prepare_data(self, df: pd.DataFrame) -> Tuple[List[str], np.ndarray]:
        """Prepare data for training."""
        texts = df['text'].tolist()
        labels = self.label_encoder.fit_transform(df['category'])
        return texts, labels
    
    def train(self, df: pd.DataFrame) -> Dict:
        """Train the classifier on the dataset."""
        print(f"Training {self.embedding_type} classifier...")
        
        # Prepare data
        texts, labels = self.prepare_data(df)
        
        # Generate embeddings
        print("Generating embeddings...")
        embeddings = self.embedding_model.fit_transform(texts)
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            embeddings, labels,
            test_size=TRAINING_CONFIG['test_size'],
            random_state=TRAINING_CONFIG['random_state'],
            stratify=labels
        )
        
        # Train classifier
        print("Training logistic regression classifier...")
        self.classifier.fit(X_train, y_train)
        
        # Evaluate
        train_score = self.classifier.score(X_train, y_train)
        test_score = self.classifier.score(X_test, y_test)
        
        # Cross-validation
        cv_scores = cross_val_score(
            self.classifier, embeddings, labels,
            cv=TRAINING_CONFIG['cv_folds'],
            scoring='accuracy'
        )
        
        # Detailed evaluation
        y_pred = self.classifier.predict(X_test)
        precision, recall, f1, _ = precision_recall_fscore_support(y_test, y_pred, average='weighted')
        
        self.is_trained = True
        
        results = {
            'embedding_type': self.embedding_type,
            'train_accuracy': train_score,
            'test_accuracy': test_score,
            'cv_mean': cv_scores.mean(),
            'cv_std': cv_scores.std(),
            'precision': precision,
            'recall': recall,
            'f1_score': f1,
            'classification_report': classification_report(
                y_test, y_pred,
                target_names=self.label_encoder.classes_,
                output_dict=True
            ),
            'confusion_matrix': confusion_matrix(y_test, y_pred).tolist()
        }
        
        print(f"Training completed for {self.embedding_type}")
        print(f"Test Accuracy: {test_score:.4f}")
        print(f"CV Accuracy: {cv_scores.mean():.4f} (+/- {cv_scores.std() * 2:.4f})")
        
        return results
    
    def predict(self, texts: List[str]) -> Tuple[List[str], List[float]]:
        """Predict categories for texts."""
        if not self.is_trained:
            raise ValueError("Model must be trained before prediction")
        
        # Generate embeddings
        embeddings = self.embedding_model.transform(texts)
        
        # Predict
        predictions = self.classifier.predict(embeddings)
        probabilities = self.classifier.predict_proba(embeddings)
        
        # Convert to category names
        predicted_categories = self.label_encoder.inverse_transform(predictions)
        
        # Get confidence scores (max probability)
        confidence_scores = np.max(probabilities, axis=1)
        
        return predicted_categories.tolist(), confidence_scores.tolist()
    
    def predict_with_probabilities(self, texts: List[str]) -> Dict:
        """Predict with full probability distribution."""
        if not self.is_trained:
            raise ValueError("Model must be trained before prediction")

        # Generate embeddings
        embeddings = self.embedding_model.transform(texts)

        # Predict
        predictions = self.classifier.predict(embeddings)
        probabilities = self.classifier.predict_proba(embeddings)
        
        # Convert to category names
        predicted_categories = self.label_encoder.inverse_transform(predictions)
        category_names = self.label_encoder.classes_
        
        results = []
        for i, text in enumerate(texts):
            result = {
                'text': text,
                'predicted_category': predicted_categories[i],
                'confidence': float(np.max(probabilities[i])),
                'probabilities': {
                    category: float(prob)
                    for category, prob in zip(category_names, probabilities[i])
                }
            }
            results.append(result)
        
        return results
    
    def save_model(self) -> None:
        """Save the trained model."""
        if not self.is_trained:
            raise ValueError("Model must be trained before saving")
        
        os.makedirs(MODELS_DIR, exist_ok=True)
        
        # Save embedding model
        self.embedding_model.save_model(self.embedding_model_path)
        
        # Save classifier
        joblib.dump(self.classifier, self.classifier_path)
        
        # Save label encoder
        joblib.dump(self.label_encoder, self.label_encoder_path)
        
        print(f"Model saved: {self.embedding_type}")
    
    def load_model(self) -> bool:
        """Load a trained model."""
        try:
            # Load embedding model
            if not self.embedding_model.load_model(self.embedding_model_path):
                return False
            
            # Load classifier
            if not os.path.exists(self.classifier_path):
                return False
            self.classifier = joblib.load(self.classifier_path)
            
            # Load label encoder
            if not os.path.exists(self.label_encoder_path):
                return False
            self.label_encoder = joblib.load(self.label_encoder_path)
            
            self.is_trained = True
            print(f"Model loaded: {self.embedding_type}")
            return True
            
        except Exception as e:
            print(f"Error loading model: {e}")
            return False

class MultiModelClassifier:
    """Manages multiple classifiers for comparison."""
    
    def __init__(self, embedding_types: List[str] = None):
        if embedding_types is None:
            embedding_types = list(MODEL_CONFIGS.keys())
        
        self.embedding_types = embedding_types
        self.classifiers = {
            emb_type: ArticleClassifier(emb_type)
            for emb_type in embedding_types
        }
        self.results = {}
    
    def train_all(self, df: pd.DataFrame) -> Dict:
        """Train all classifiers."""
        print("Training all classifiers...")
        
        for emb_type in self.embedding_types:
            try:
                print(f"\n{'='*50}")
                print(f"Training {emb_type.upper()} classifier")
                print(f"{'='*50}")
                
                results = self.classifiers[emb_type].train(df)
                self.results[emb_type] = results
                
                # Save model
                self.classifiers[emb_type].save_model()
                
            except Exception as e:
                print(f"Error training {emb_type}: {e}")
                self.results[emb_type] = {'error': str(e)}
        
        return self.results
    
    def compare_models(self) -> pd.DataFrame:
        """Compare performance of all models."""
        comparison_data = []
        
        for emb_type, results in self.results.items():
            if 'error' not in results:
                comparison_data.append({
                    'Model': MODEL_CONFIGS[emb_type]['name'],
                    'Embedding Type': emb_type,
                    'Test Accuracy': results['test_accuracy'],
                    'CV Mean': results['cv_mean'],
                    'CV Std': results['cv_std'],
                    'Precision': results['precision'],
                    'Recall': results['recall'],
                    'F1 Score': results['f1_score']
                })
        
        return pd.DataFrame(comparison_data)
    
    def predict_all(self, texts: List[str]) -> Dict:
        """Get predictions from all trained models."""
        all_predictions = {}
        
        for emb_type in self.embedding_types:
            try:
                if self.classifiers[emb_type].is_trained:
                    predictions = self.classifiers[emb_type].predict_with_probabilities(texts)
                    all_predictions[emb_type] = predictions
            except Exception as e:
                print(f"Error predicting with {emb_type}: {e}")
                all_predictions[emb_type] = {'error': str(e)}
        
        return all_predictions

if __name__ == "__main__":
    # Test classifier
    print("Testing classifier implementation...")
    
    # Load sample data
    df = pd.read_csv("../data/train_dataset.csv")
    print(f"Loaded {len(df)} training samples")
    
    # Test single classifier
    classifier = ArticleClassifier("word2vec")
    results = classifier.train(df)
    print("Training results:", results)
