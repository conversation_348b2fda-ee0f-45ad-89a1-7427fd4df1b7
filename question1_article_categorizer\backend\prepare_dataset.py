"""
Data preparation script for article categorization dataset.
Creates sample articles for 6 categories: Tech, Finance, Healthcare, Sports, Politics, Entertainment
"""

import pandas as pd
import os
import json
from typing import List, Dict

# Sample articles for each category
SAMPLE_ARTICLES = {
    "Tech": [
        "Apple unveiled its latest iPhone with advanced AI capabilities and improved camera technology. The new device features a faster processor and enhanced battery life.",
        "Google's new AI model demonstrates breakthrough performance in natural language processing tasks, outperforming previous models by significant margins.",
        "Microsoft announces major updates to its cloud computing platform, introducing new machine learning tools for developers and enterprises.",
        "Tesla's latest software update includes enhanced autopilot features and improved energy efficiency for electric vehicles.",
        "Meta introduces new virtual reality headset with improved resolution and reduced latency for immersive gaming experiences.",
        "Amazon Web Services launches new quantum computing service for researchers and enterprises exploring quantum algorithms.",
        "NVIDIA's latest graphics cards deliver unprecedented performance for gaming and AI workloads with advanced ray tracing capabilities.",
        "SpaceX successfully launches another batch of Starlink satellites, expanding global internet coverage to remote areas.",
        "Intel announces breakthrough in semiconductor manufacturing with new 3nm process technology for next-generation processors.",
        "OpenAI releases new language model with improved reasoning capabilities and reduced hallucination rates."
    ],
    "Finance": [
        "Federal Reserve announces interest rate decision amid inflation concerns and economic uncertainty in global markets.",
        "Stock market reaches new highs as technology companies report strong quarterly earnings and revenue growth.",
        "Cryptocurrency market experiences volatility as regulatory frameworks evolve across different countries and jurisdictions.",
        "Major banks report increased profits from lending activities and improved credit quality in consumer portfolios.",
        "Investment firms recommend diversified portfolios as economic indicators show mixed signals for future growth.",
        "Real estate market shows signs of cooling as mortgage rates increase and housing inventory remains limited.",
        "Gold prices surge amid geopolitical tensions and concerns about currency devaluation in emerging markets.",
        "Corporate earnings season reveals strong performance in technology sector while retail faces ongoing challenges.",
        "Central banks coordinate monetary policy responses to address global inflation and supply chain disruptions.",
        "Venture capital funding reaches record levels as startups in AI and clean energy attract significant investments."
    ],
    "Healthcare": [
        "New cancer treatment shows promising results in clinical trials, offering hope for patients with advanced stages.",
        "COVID-19 vaccine effectiveness remains high against new variants according to latest epidemiological studies.",
        "Mental health awareness campaigns highlight importance of early intervention and accessible treatment options.",
        "Medical researchers develop innovative gene therapy approach for treating rare genetic disorders in children.",
        "Healthcare systems implement AI-powered diagnostic tools to improve accuracy and reduce physician workload.",
        "Pharmaceutical companies collaborate on drug development for Alzheimer's disease using novel therapeutic targets.",
        "Telemedicine adoption continues to grow as patients and providers embrace remote healthcare delivery models.",
        "Public health officials recommend updated vaccination schedules based on latest immunological research findings.",
        "Medical device manufacturers introduce wearable technology for continuous monitoring of chronic conditions.",
        "Healthcare policy reforms aim to improve access and affordability while maintaining quality of care standards."
    ],
    "Sports": [
        "World Cup final draws record television audience as two powerhouse teams compete for championship title.",
        "Olympic Games showcase incredible athletic performances and inspire next generation of sports enthusiasts.",
        "Professional basketball season reaches playoffs with intense competition and unexpected upsets throughout.",
        "Tennis tournament features thrilling matches between top-ranked players from around the world.",
        "Baseball season concludes with dramatic World Series featuring historic performances and memorable moments.",
        "Football championship game delivers excitement as teams battle for ultimate prize in front of packed stadium.",
        "Swimming records broken at international competition as athletes push boundaries of human performance.",
        "Golf tournament attracts global audience as professional players compete on challenging championship course.",
        "Marathon event brings together thousands of runners from diverse backgrounds united by common goal.",
        "Winter sports season showcases skiing, snowboarding, and ice hockey competitions across multiple venues."
    ],
    "Politics": [
        "Presidential election campaign intensifies as candidates present policy platforms to voters nationwide.",
        "Congressional hearing addresses important legislation affecting healthcare, education, and economic policy.",
        "International summit brings together world leaders to discuss climate change and global cooperation.",
        "Supreme Court decision impacts constitutional rights and sets precedent for future legal challenges.",
        "Local government implements new policies to address housing affordability and urban development challenges.",
        "Diplomatic negotiations continue as nations work to resolve trade disputes and strengthen relationships.",
        "Voter registration drives encourage civic participation ahead of upcoming elections at all levels.",
        "Policy debate focuses on infrastructure investment and its potential impact on economic growth.",
        "Government transparency initiatives aim to increase public access to information and decision-making processes.",
        "Bipartisan legislation addresses national security concerns while protecting civil liberties and privacy rights."
    ],
    "Entertainment": [
        "Hollywood blockbuster breaks box office records with stunning visual effects and compelling storyline.",
        "Music festival features diverse lineup of artists performing across multiple genres and stages.",
        "Television series finale draws massive viewership as beloved characters conclude their journey.",
        "Celebrity awards ceremony celebrates outstanding achievements in film, television, and music industries.",
        "Streaming platform announces new original content including documentaries, series, and feature films.",
        "Broadway show receives critical acclaim for innovative staging and powerful performances by talented cast.",
        "Video game release generates excitement among gaming community with immersive gameplay and graphics.",
        "Art exhibition showcases contemporary works from emerging and established artists around the world.",
        "Fashion week highlights latest trends and designs from renowned designers and emerging talent.",
        "Book release becomes bestseller as readers embrace compelling narrative and memorable characters."
    ]
}

def create_dataset() -> pd.DataFrame:
    """Create a balanced dataset from sample articles."""
    data = []
    
    for category, articles in SAMPLE_ARTICLES.items():
        for article in articles:
            data.append({
                'text': article,
                'category': category
            })
    
    df = pd.DataFrame(data)
    
    # Shuffle the dataset
    df = df.sample(frac=1, random_state=42).reset_index(drop=True)
    
    return df

def save_dataset(df: pd.DataFrame, data_dir: str = None):
    """Save dataset in multiple formats."""
    if data_dir is None:
        data_dir = os.path.join(os.path.dirname(__file__), "..", "data")
    
    os.makedirs(data_dir, exist_ok=True)
    
    # Save as CSV
    df.to_csv(os.path.join(data_dir, "articles_dataset.csv"), index=False)
    
    # Save as JSON
    df.to_json(os.path.join(data_dir, "articles_dataset.json"), orient="records", indent=2)
    
    # Create train/test split
    from sklearn.model_selection import train_test_split
    
    train_df, test_df = train_test_split(df, test_size=0.2, stratify=df['category'], random_state=42)
    
    train_df.to_csv(os.path.join(data_dir, "train_dataset.csv"), index=False)
    test_df.to_csv(os.path.join(data_dir, "test_dataset.csv"), index=False)
    
    # Save category mapping
    categories = df['category'].unique().tolist()
    category_mapping = {cat: idx for idx, cat in enumerate(categories)}
    
    with open(os.path.join(data_dir, "category_mapping.json"), "w") as f:
        json.dump(category_mapping, f, indent=2)
    
    print(f"Dataset created with {len(df)} articles")
    print(f"Categories: {categories}")
    print(f"Distribution:\n{df['category'].value_counts()}")
    print(f"Train set: {len(train_df)} articles")
    print(f"Test set: {len(test_df)} articles")

if __name__ == "__main__":
    # Create dataset
    df = create_dataset()
    
    # Save dataset
    save_dataset(df)
    
    print("Dataset preparation completed!")
