# Smart Article Categorizer

A comprehensive article classification system that compares 4 different embedding approaches for text classification. Built with React frontend and FastAPI backend.

## 🎯 **Project Overview**

This project implements and compares four different embedding techniques for article classification:

1. **Word2Vec/TF-IDF**: Traditional word embeddings with document averaging
2. **BERT**: Transformer-based embeddings using [CLS] token
3. **Sentence-BERT**: Direct sentence embeddings optimized for semantic similarity
4. **OpenAI**: State-of-the-art embeddings using text-embedding-ada-002

## 🏗️ **Architecture**

```
question1_article_categorizer/
├── backend/                    # Python FastAPI backend
│   ├── api.py                 # FastAPI server with endpoints
│   ├── classifier.py          # Article classifier classes
│   ├── config.py              # Configuration settings
│   ├── embeddings.py          # Embedding implementations
│   ├── compare_models.py      # Model comparison utilities
│   ├── prepare_dataset.py     # Dataset preparation
│   └── train_*.py             # Individual model training scripts
├── frontend/                  # React Vite application
│   ├── src/
│   │   ├── App.jsx           # Main React component
│   │   ├── App.css           # Styling with Tailwind CSS
│   │   └── index.css         # Global styles
│   └── package.json          # Frontend dependencies
├── data/                      # Datasets and mappings
├── models/                    # Trained model files
└── results/                   # Training results and analysis
```

## 🚀 **Features**

### Backend (FastAPI)
- **Multi-model API**: Simultaneous predictions from all 4 models
- **Individual model endpoints**: Test specific models independently
- **Batch processing**: Handle multiple texts at once
- **Performance metrics**: Accuracy, precision, recall, F1-score
- **CORS enabled**: Frontend-backend communication
- **OpenAI integration**: Secure API key handling

### Frontend (React)
- **Full-width responsive design**: Optimized for all screen sizes
- **Real-time predictions**: Instant classification from all models
- **Confidence visualization**: Color-coded confidence scores
- **Best model highlighting**: Automatic identification of highest confidence
- **Smooth animations**: Modern UI with transitions and hover effects
- **Category overview**: Visual display of all available categories

## 📊 **Model Performance**

Based on comprehensive testing:

| Model | Test Accuracy | Confidence Range | Best For |
|-------|---------------|------------------|----------|
| **BERT** | ~65% | 40-90% | Technical content, complex semantics |
| **Sentence-BERT** | ~41% | 30-60% | Semantic similarity, general text |
| **OpenAI** | ~24% | 20-40% | Diverse content, robust performance |
| **Word2Vec/TF-IDF** | ~23% | 15-35% | Keyword-based classification |

## 🛠️ **Installation & Setup**

### Prerequisites
- Python 3.8+
- Node.js 16+
- OpenAI API key

### Backend Setup
```bash
cd question1_article_categorizer/backend
pip install -r requirements.txt
python api.py
```

### Frontend Setup
```bash
cd question1_article_categorizer/frontend
npm install
npm run dev
```

## 🔧 **Configuration**

### OpenAI API Key
Set your OpenAI API key in `backend/config.py`:
```python
OPENAI_API_KEY = "your-api-key-here"
```

### Model Configuration
Each model can be configured in `backend/config.py`:
- Batch sizes
- Model names
- Training parameters
- File paths

## 📡 **API Endpoints**

### Health Check
```
GET /
```

### Get Available Models
```
GET /models
```

### Single Model Prediction
```
POST /predict
{
  "text": "Your article text here",
  "model_type": "bert"
}
```

### Multi-Model Prediction
```
POST /predict-all
{
  "text": "Your article text here"
}
```

### Get Categories
```
GET /categories
```

## 🧪 **Testing**

### Manual Testing
1. Start both backend and frontend servers
2. Open http://localhost:5174
3. Enter test text and click "Classify with All Models"
4. Verify all 4 models return predictions

### API Testing
```bash
# Test multi-model endpoint
curl -X POST "http://localhost:8000/predict-all" \
  -H "Content-Type: application/json" \
  -d '{"text": "Apple announced new iPhone with AI features"}'
```

## 📈 **Model Training**

Each model can be trained individually:

```bash
# Train Word2Vec model
python backend/train_word2vec.py

# Train BERT model
python backend/train_bert.py

# Train Sentence-BERT model
python backend/train_sentence_bert.py

# Train OpenAI model
python backend/train_openai_model.py
```

## 🎨 **UI Features**

- **Full-screen layout**: Utilizes entire viewport width
- **Responsive grid**: 1-4 columns based on screen size
- **Smooth animations**: Hover effects and transitions
- **Color-coded confidence**: Visual confidence indicators
- **Modern design**: Gradient backgrounds and shadows

## 🔍 **Categories**

The system classifies articles into 6 categories:
- **Tech**: Technology and innovation
- **Finance**: Financial news and markets
- **Healthcare**: Medical and health topics
- **Sports**: Sports news and events
- **Politics**: Political news and analysis
- **Entertainment**: Entertainment and celebrity news

## 🚨 **Troubleshooting**

### Common Issues
1. **BERT model errors**: Ensure PyTorch is properly installed
2. **OpenAI API errors**: Verify API key is valid and has credits
3. **Frontend styling issues**: Check Tailwind CSS configuration
4. **CORS errors**: Ensure backend CORS is properly configured

### Performance Optimization
- Use GPU for BERT if available
- Adjust batch sizes based on memory
- Cache model loading for faster startup

## 📝 **License**

This project is for educational purposes as part of the RAG+Langchain workshop.

## 🤝 **Contributing**

This is a workshop project. For improvements:
1. Fork the repository
2. Create feature branch
3. Submit pull request with detailed description

---

**Built with ❤️ for the RAG+Langchain Workshop**
