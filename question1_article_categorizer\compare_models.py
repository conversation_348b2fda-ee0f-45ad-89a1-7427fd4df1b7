"""
Comprehensive model comparison script for all embedding approaches.
"""

import pandas as pd
import numpy as np
# import matplotlib.pyplot as plt
# import seaborn as sns
import os
import sys
import json
from datetime import datetime

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from classifier import ArticleClassifier, MultiModelClassifier
from config import create_directories

def load_test_data():
    """Load test dataset."""
    test_path = os.path.join("data", "test_dataset.csv")
    if not os.path.exists(test_path):
        print(f"Test data not found at {test_path}")
        return None
    return pd.read_csv(test_path)

def evaluate_single_model(model_type):
    """Evaluate a single model type."""
    print(f"\nEvaluating {model_type} model...")
    
    try:
        # Load model
        classifier = ArticleClassifier(model_type)
        model_path = os.path.join("models", f"{model_type}_classifier.pkl")
        
        if not os.path.exists(model_path):
            print(f"Model not found: {model_path}")
            return None
        
        classifier.load_model()
        
        # Load test data
        test_df = load_test_data()
        if test_df is None:
            return None
        
        # Make predictions
        predictions = classifier.predict(test_df['text'].tolist())
        probabilities = classifier.predict_proba(test_df['text'].tolist())
        
        # Calculate metrics
        from sklearn.metrics import accuracy_score, precision_recall_fscore_support, classification_report
        
        accuracy = accuracy_score(test_df['category'], predictions)
        precision, recall, f1, _ = precision_recall_fscore_support(
            test_df['category'], predictions, average='weighted'
        )
        
        # Get detailed classification report
        report = classification_report(
            test_df['category'], predictions, 
            output_dict=True
        )
        
        results = {
            'model_type': model_type,
            'accuracy': accuracy,
            'precision': precision,
            'recall': recall,
            'f1_score': f1,
            'classification_report': report,
            'predictions': predictions.tolist(),
            'probabilities': probabilities.tolist() if probabilities is not None else None
        }
        
        print(f"{model_type} - Accuracy: {accuracy:.4f}, F1: {f1:.4f}")
        return results
        
    except Exception as e:
        print(f"Error evaluating {model_type}: {e}")
        return None

def create_comparison_plots(results_list):
    """Create comparison plots."""
    if not results_list:
        return
    
    # Extract metrics
    models = [r['model_type'] for r in results_list]
    accuracies = [r['accuracy'] for r in results_list]
    precisions = [r['precision'] for r in results_list]
    recalls = [r['recall'] for r in results_list]
    f1_scores = [r['f1_score'] for r in results_list]
    
    # Create comparison plot
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    
    # Accuracy comparison
    bars1 = ax1.bar(models, accuracies, color=['skyblue', 'lightgreen', 'lightcoral'])
    ax1.set_title('Model Accuracy Comparison')
    ax1.set_ylabel('Accuracy')
    ax1.set_ylim(0, 1)
    for i, v in enumerate(accuracies):
        ax1.text(i, v + 0.01, f'{v:.3f}', ha='center')
    
    # Precision comparison
    bars2 = ax2.bar(models, precisions, color=['skyblue', 'lightgreen', 'lightcoral'])
    ax2.set_title('Model Precision Comparison')
    ax2.set_ylabel('Precision')
    ax2.set_ylim(0, 1)
    for i, v in enumerate(precisions):
        ax2.text(i, v + 0.01, f'{v:.3f}', ha='center')
    
    # Recall comparison
    bars3 = ax3.bar(models, recalls, color=['skyblue', 'lightgreen', 'lightcoral'])
    ax3.set_title('Model Recall Comparison')
    ax3.set_ylabel('Recall')
    ax3.set_ylim(0, 1)
    for i, v in enumerate(recalls):
        ax3.text(i, v + 0.01, f'{v:.3f}', ha='center')
    
    # F1-Score comparison
    bars4 = ax4.bar(models, f1_scores, color=['skyblue', 'lightgreen', 'lightcoral'])
    ax4.set_title('Model F1-Score Comparison')
    ax4.set_ylabel('F1-Score')
    ax4.set_ylim(0, 1)
    for i, v in enumerate(f1_scores):
        ax4.text(i, v + 0.01, f'{v:.3f}', ha='center')
    
    plt.tight_layout()
    plt.savefig(os.path.join("results", "model_comparison.png"), dpi=300, bbox_inches='tight')
    plt.show()

def generate_report(results_list):
    """Generate comprehensive comparison report."""
    if not results_list:
        return
    
    report = {
        'timestamp': datetime.now().isoformat(),
        'summary': {},
        'detailed_results': results_list
    }
    
    # Create summary
    best_accuracy = max(results_list, key=lambda x: x['accuracy'])
    best_f1 = max(results_list, key=lambda x: x['f1_score'])
    
    report['summary'] = {
        'best_accuracy_model': best_accuracy['model_type'],
        'best_accuracy_score': best_accuracy['accuracy'],
        'best_f1_model': best_f1['model_type'],
        'best_f1_score': best_f1['f1_score'],
        'model_rankings': sorted(results_list, key=lambda x: x['f1_score'], reverse=True)
    }
    
    # Save report
    os.makedirs("results", exist_ok=True)
    with open(os.path.join("results", "comparison_report.json"), 'w') as f:
        json.dump(report, f, indent=2)
    
    # Print summary
    print("\n" + "="*60)
    print("MODEL COMPARISON SUMMARY")
    print("="*60)
    print(f"Best Accuracy: {best_accuracy['model_type']} ({best_accuracy['accuracy']:.4f})")
    print(f"Best F1-Score: {best_f1['model_type']} ({best_f1['f1_score']:.4f})")
    print("\nModel Rankings (by F1-Score):")
    for i, model in enumerate(report['summary']['model_rankings'], 1):
        print(f"{i}. {model['model_type']}: F1={model['f1_score']:.4f}, Acc={model['accuracy']:.4f}")

def main():
    """Main comparison function."""
    print("Starting comprehensive model comparison...")
    
    # Create directories
    create_directories()
    os.makedirs("results", exist_ok=True)
    
    # Models to evaluate
    models = ['word2vec', 'bert', 'sentence_bert']
    
    # Evaluate all models
    results_list = []
    for model_type in models:
        result = evaluate_single_model(model_type)
        if result:
            results_list.append(result)
    
    if not results_list:
        print("No models could be evaluated!")
        return
    
    # Create comparison plots (commented out for now)
    # create_comparison_plots(results_list)
    
    # Generate comprehensive report
    generate_report(results_list)
    
    print(f"\nComparison completed! Results saved in 'results' directory.")

if __name__ == "__main__":
    main()
